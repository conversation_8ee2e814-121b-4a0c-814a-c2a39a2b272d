/**
 * 计算总成本费用表
 * @param {Object} params - 输入参数
 * @param {boolean} showLogs - 是否显示表格数据（可选，默认为false）
 * @returns {Object} - 总成本费用表结果
 */
function calculateTotalCostAndExpenses(params, showLogs = false) {
    const {
        operatingYears, // 项目运营年限
        incomeTaxRate, // 所得税率
        salvageRate, // 残值率
        windTurbineDepreciationYears, // 风机折旧年限
        photovoltaicDepreciationYears, // 光伏折旧年限
        energyStorageDepreciationYears, // 储能折旧年限
        electrolyzerDepreciationYears, // 电解槽折旧年限
        hydrogenStorageDepreciationYears, // 储氢折旧年限
            
        // 拆分发电侧资产
        fixedWindTurbineAssetsNoTax, // 固定风机资产不含税
        fixedPhotovoltaicAssetsNoTax, // 固定光伏资产不含税
        fixedEnergyStorageAssetsNoTax, // 固定储能资产不含税
        windTurbineEquipmentAndInstallationWithTax, // 风机设备及安装含税
        photovoltaicEquipmentAndInstallationWithTax, // 光伏设备及安装含税
        energyStorageEquipmentAndInstallationWithTax, // 储能设备及安装含税
        fixedWindTurbineAssetsWithTax, // 固定风机资产含税
        fixedPhotovoltaicAssetsWithTax, // 固定光伏资产含税
        fixedEnergyStorageAssetsWithTax, // 固定储能资产含税
        windTurbineEquipmentAndInstallationNoTax, // 风机设备及安装不含税
        photovoltaicEquipmentAndInstallationNoTax, // 光伏设备及安装不含税
        energyStorageEquipmentAndInstallationNoTax, // 储能设备及安装不含税
        
        // 拆分制氢侧资产
        fixedElectrolyzerAssetsNoTax, // 固定电解槽资产不含税
        fixedHydrogenStorageAssetsNoTax, // 固定储罐资产不含税
        electrolyzerEquipmentAndInstallationWithTax, // 电解槽设备及安装含税
        hydrogenStorageEquipmentAndInstallationWithTax, // 储罐设备及安装含税
        fixedElectrolyzerAssetsWithTax, // 固定电解槽资产含税
        fixedHydrogenStorageAssetsWithTax, // 固定储罐资产含税
        electrolyzerEquipmentAndInstallationNoTax, // 电解槽设备及安装不含税
        hydrogenStorageEquipmentAndInstallationNoTax, // 储罐设备及安装不含税
        
        // 土地租赁费和税费
        windTurbineLandRentalFeeYear2, // 风机第2年土地租赁费
        photovoltaicLandRentalFeeYear2, // 光伏第2年土地租赁费
        windTurbineLandRentalFeeGrowthRate, // 风机土地租赁费增长率
        photovoltaicLandRentalFeeGrowthRate, // 光伏土地租赁费增长率
        windTurbineLandTaxAnnual, // 风机年度土地税费
        photovoltaicLandTaxAnnual, // 光伏年度土地税费
        
        // 各组件其他费用单价
        windTurbineOtherCostUnitPrice, // 风机其他费用单价
        photovoltaicOtherCostUnitPrice, // 光伏其他费用单价
        energyStorageOtherCostUnitPrice, // 储能其他费用单价
        electrolyzerOtherCostUnitPrice, // 电解槽其他费用单价
        hydrogenStorageOtherCostUnitPrice, // 储氢其他费用单价
        
        // 各组件其他费用增长率
        windTurbineOtherCostGrowthRate, // 风机其他费用年增长率
        photovoltaicOtherCostGrowthRate, // 光伏其他费用年增长率
        energyStorageOtherCostGrowthRate, // 储能其他费用年增长率
        electrolyzerOtherCostGrowthRate, // 电解槽其他费用年增长率
        hydrogenStorageOtherCostGrowthRate, // 储氢其他费用年增长率
        
        // 其他参数
        waterConsumption, // 第N年耗水量
        waterPriceNoTax, // 水费单价不含税
        greenHydrogenElectricity, // 第N年绿电制氢电量
        selfPowerPlantElectricityPriceNoTax, // 自备电厂不含税电价
        gridHydrogenElectricity, // 第N年下网制氢电量
        gridHydrogenElectricityPriceNoTax, // 下网不含税电价
        selfPowerPlantElectricityPriceWithTax, // 自备电厂含税电价
        hydrogenProduction, // 第N年制氢量
        
        // 拆分后的材料费率
        windTurbineMaterialsCostProvisionRate, // 风机材料费计提率
        photovoltaicMaterialsCostProvisionRate, // 光伏材料费计提率
        energyStorageMaterialsCostProvisionRate, // 储能材料费计提率
        electrolyzerMaterialsCostProvisionRate, // 电解槽材料费计提率
        hydrogenStorageMaterialsCostProvisionRate, // 储氢材料费计提率
        
        // 拆分后的材料费增长率
        windTurbineMaterialsCostAnnualGrowthRate, // 风机材料费年均增长率
        photovoltaicMaterialsCostAnnualGrowthRate, // 光伏材料费年均增长率
        energyStorageMaterialsCostAnnualGrowthRate, // 储能材料费年均增长率
        electrolyzerMaterialsCostAnnualGrowthRate, // 电解槽材料费年均增长率
        hydrogenStorageMaterialsCostAnnualGrowthRate, // 储氢材料费年均增长率
        
        totalCostSensitivityFactor, // 总成本敏感系数
        transportationRateNoTax, // 运输费率不含税
        transportDistance, // 运输距离
        
        // 拆分后的大修更换费率
        windTurbineMajorOverhaulReplacementRate, // 风机大修更换费率
        photovoltaicMajorOverhaulReplacementRate, // 光伏大修更换费率
        energyStorageMajorOverhaulReplacementRate, // 储能大修更换费率
        electrolyzerMajorOverhaulReplacementRate, // 电解槽大修更换费率
        hydrogenStorageMajorOverhaulReplacementRate, // 储氢大修更换费率
        
        installedCapacity, // 装机量
        pvCapacity, // 光伏装机量
        windCapacity, // 风机装机量
        batCapacity, // 储能装机量
        alkCapacity, // 电解槽装机量
        alkStoreCapacity, // 储氢装机量
        powerGenerationCostSensitivityFactor, // 发电成本敏感系数
        hydrogenProductionCostSensitivityFactor, // 制氢成本敏感系数
        waterResourceTaxUnitPrice, // 水资源税单价
        workerCount, // 工人数量
        workerMonthlySalary, // 工人人均月薪
        technicianCount, // 技术人员数量
        technicianMonthlySalary, // 技术人均月薪
        managerCount, // 管理人员数量
        managerMonthlySalary, // 管理人均月薪
        socialSecurityAndWelfareFactor, // 福利社保系数
        salaryAnnualGrowthRate, // 薪资年增长率
        maintenanceCostVATRate, // 维修费增值税率
        materialsCostVATRate, // 材料费增值税率
        transportationFeeVATRate, // 运输费增值税率
        waterFeeVATRate, // 水费增值税率
        electricityFeeVATRate, // 电费增值税率
        annualInterest, // 第N年本年付息
        shortTermLoanInterest, // 第N年短期借款利息
        workingCapitalInterest, // 第N年流动资金利息
        
        // 拆分后的维修费率
        windTurbineMaintCostRateYear2, // 风机第2年维修费率
        photovoltaicMaintCostRateYear2, // 光伏第2年维修费率
        energyStorageMaintCostRateYear2, // 储能第2年维修费率
        electrolyzerMaintCostRateYear2, // 电解槽第2年维修费率
        hydrogenStorageMaintCostRateYear2, // 储氢第2年维修费率
        
        // 拆分后的维修费增长率
        windTurbineMaintCostGrowthRateYears7to11, // 风机第7到11年维修费年增长率
        windTurbineMaintCostGrowthRateYears12plus, // 风机第12到N年维修费年增长率
        photovoltaicMaintCostGrowthRateYears7to11, // 光伏第7到11年维修费年增长率
        photovoltaicMaintCostGrowthRateYears12plus, // 光伏第12到N年维修费年增长率
        energyStorageMaintCostGrowthRateYears7to11, // 储能第7到11年维修费年增长率
        energyStorageMaintCostGrowthRateYears12plus, // 储能第12到N年维修费年增长率
        electrolyzerMaintCostGrowthRateYears7to11, // 电解槽第7到11年维修费年增长率
        electrolyzerMaintCostGrowthRateYears12plus, // 电解槽第12到N年维修费年增长率
        hydrogenStorageMaintCostGrowthRateYears7to11, // 储氢第7到11年维修费年增长率
        hydrogenStorageMaintCostGrowthRateYears12plus, // 储氢第12到N年维修费年增长率
        
        // 拆分后的保险费率
        windTurbineInsuranceRate, // 风机保险费率
        photovoltaicInsuranceRate, // 光伏保险费率
        energyStorageInsuranceRate, // 储能保险费率
        electrolyzerInsuranceRate, // 电解槽保险费率
        hydrogenStorageInsuranceRate, // 储氢保险费率

        publicLoadCapacity, // 公辅装置容量
        publicLoadHour, // 公辅装置运行小时数
        initInfo
    } = params;

    // 创建结果对象，包括所有年份的数据
    const years = operatingYears + 1; // +1 是因为包括第1年(建设期)
    const results = {
        incomeTaxRate: new Array(years).fill(0), // 第N年所得税率
        
        // 原始折旧费用结果
        depreciation: new Array(years).fill(0), // 第N年折旧费
        powerDepreciation: new Array(years).fill(0), // 第N年发电折旧费
        hydrogenDepreciation: new Array(years).fill(0), // 第N年制氢折旧费
        
        // 拆分后的折旧费用结果
        windTurbineDepreciation: new Array(years).fill(0), // 第N年风机折旧费
        photovoltaicDepreciation: new Array(years).fill(0), // 第N年光伏折旧费
        energyStorageDepreciation: new Array(years).fill(0), // 第N年储能折旧费
        electrolyzerDepreciation: new Array(years).fill(0), // 第N年电解槽折旧费
        hydrogenStorageDepreciation: new Array(years).fill(0), // 第N年储罐折旧费
        
        // 原始维修费用结果
        maintenanceCost: new Array(years).fill(0), // 第N年维修费
        powerMaintenanceCost: new Array(years).fill(0), // 第N年发电维修费
        hydrogenMaintenanceCost: new Array(years).fill(0), // 第N年制氢维修费
        
        // 拆分后的维修费用结果
        windTurbineMaintenanceCost: new Array(years).fill(0), // 第N年风机维修费
        photovoltaicMaintenanceCost: new Array(years).fill(0), // 第N年光伏维修费
        energyStorageMaintenanceCost: new Array(years).fill(0), // 第N年储能维修费
        electrolyzerMaintenanceCost: new Array(years).fill(0), // 第N年电解槽维修费
        hydrogenStorageMaintenanceCost: new Array(years).fill(0), // 第N年储罐维修费
        
        // 原始保险费用结果
        insuranceCost: new Array(years).fill(0), // 第N年保险费
        powerInsuranceCost: new Array(years).fill(0), // 第N年发电保险费
        hydrogenInsuranceCost: new Array(years).fill(0), // 第N年制氢保险费
        
        // 拆分后的保险费用结果
        windTurbineInsuranceCost: new Array(years).fill(0), // 第N年风机保险费
        photovoltaicInsuranceCost: new Array(years).fill(0), // 第N年光伏保险费
        energyStorageInsuranceCost: new Array(years).fill(0), // 第N年储能保险费
        electrolyzerInsuranceCost: new Array(years).fill(0), // 第N年电解槽保险费
        hydrogenStorageInsuranceCost: new Array(years).fill(0), // 第N年储罐保险费
        
        // 原始水费结果
        waterCost: new Array(years).fill(0), // 第N年水费
        hydrogenWaterCost: new Array(years).fill(0), // 第N年制氢水费
        
        // 拆分后的水费结果 - 只有电解槽用水
        electrolyzerWaterCost: new Array(years).fill(0), // 第N年电解槽水费
        
        // 原始电费结果
        electricityCost: new Array(years).fill(0), // 第N年电费
        hydrogenElectricityCost: new Array(years).fill(0), // 第N年制氢电费
        
        // 拆分后的电费结果 - 只有电解槽用电
        electrolyzerElectricityCost: new Array(years).fill(0), // 第N年电解槽电费
        
        // 原始材料费结果
        materialsCost: new Array(years).fill(0), // 第N年材料费
        
        // 拆分后的材料费结果
        windTurbineMaterialsCost: new Array(years).fill(0), // 第N年风机材料费
        photovoltaicMaterialsCost: new Array(years).fill(0), // 第N年光伏材料费
        energyStorageMaterialsCost: new Array(years).fill(0), // 第N年储能材料费
        electrolyzerMaterialsCost: new Array(years).fill(0), // 第N年电解槽材料费
        hydrogenStorageMaterialsCost: new Array(years).fill(0), // 第N年储罐材料费
        
        // 原始运输费用结果
        transportationCost: new Array(years).fill(0), // 第N年运输成本
        hydrogenTransportationCost: new Array(years).fill(0), // 第N年氢气运输成本
        
        // 拆分后的运输费用结果 - 只有电解槽产品需要运输
        electrolyzerTransportationCost: new Array(years).fill(0), // 第N年电解槽产品运输成本
        
        // 原始设备大修更换费用结果
        equipmentOverhaulReplacementCost: new Array(years).fill(0), // 第N年设备大修更换费用
        
        // 拆分后的设备大修更换费用结果
        windTurbineOverhaulReplacementCost: new Array(years).fill(0), // 第N年风机设备大修更换费用
        photovoltaicOverhaulReplacementCost: new Array(years).fill(0), // 第N年光伏设备大修更换费用
        energyStorageOverhaulReplacementCost: new Array(years).fill(0), // 第N年储能设备大修更换费用
        electrolyzerOverhaulReplacementCost: new Array(years).fill(0), // 第N年电解槽设备大修更换费用
        hydrogenStorageOverhaulReplacementCost: new Array(years).fill(0), // 第N年储罐设备大修更换费用
        
        // 原始土地费用结果
        landRentalFee: new Array(years).fill(0), // 第N年土地租赁费
        povertyAlleviationFee: new Array(years).fill(0), // 第N年扶贫规费支出
        landTaxFee: new Array(years).fill(0), // 第N年土地税费
        
        // 拆分后的土地费用结果 - 只有风机和光伏需要土地费用
        windTurbineLandRentalFee: new Array(years).fill(0), // 第N年风机土地租赁费
        photovoltaicLandRentalFee: new Array(years).fill(0), // 第N年光伏土地租赁费
        windTurbineLandTaxFee: new Array(years).fill(0), // 第N年风机土地税费
        photovoltaicLandTaxFee: new Array(years).fill(0), // 第N年光伏土地税费
        
        // 原始其他费用结果
        otherCost: new Array(years).fill(0), // 第N年其他费用
        powerOtherCost: new Array(years).fill(0), // 第N年发电其他费用
        hydrogenOtherCost: new Array(years).fill(0), // 第N年制氢其他费用
        
        // 拆分后的其他费用结果
        windTurbineOtherCost: new Array(years).fill(0), // 第N年风机其他费用
        photovoltaicOtherCost: new Array(years).fill(0), // 第N年光伏其他费用
        energyStorageOtherCost: new Array(years).fill(0), // 第N年储能其他费用
        electrolyzerOtherCost: new Array(years).fill(0), // 第N年电解槽其他费用
        hydrogenStorageOtherCost: new Array(years).fill(0), // 第N年储罐其他费用

        publicLoadPowerCost: new Array(years).fill(0), // 第N年公辅装置电费
        
        // 原始水资源税结果
        waterResourceTax: new Array(years).fill(0), // 第N年水资源税
        
        // 拆分后的水资源税结果 - 只有电解槽需要水资源税
        electrolyzerWaterResourceTax: new Array(years).fill(0), // 第N年电解槽水资源税
        
        // 其他原始结果
        laborCost: new Array(years).fill(0), // 第N年工资福利及劳保统筹和住房基金
        amortizationCost: new Array(years).fill(0), // 第N年摊销费
        maintenanceCostInputTax: new Array(years).fill(0), // 第N年维修费进项税
        materialsCostInputTax: new Array(years).fill(0), // 第N年材料成本进项税
        transportationCostInputTax: new Array(years).fill(0), // 第N年运输成本进项税
        waterCostInputTax: new Array(years).fill(0), // 第N年水费进项税
        electricityCostInputTax: new Array(years).fill(0), // 第N年电费进项税
        inputTax: new Array(years).fill(0), // 第N年进项税
        interestExpenseWithTax: new Array(years).fill(0), // 第N年含税利息支出
        fixedCost: new Array(years).fill(0), // 第N年固定成本
        variableCost: new Array(years).fill(0), // 第N年可变成本
        totalCostExpense: new Array(years).fill(0) // 第N年总成本费用
    };

    // 计算各年数据
    for (let i = 0; i < years; i++) {
        // 第N年所得税率(三免三减半)
        if (i === 0 || i === 1 || i === 2 || i === 3) { 
            results.incomeTaxRate[i] = 0; // 第一到四年所得税率=0
        } else if (i === 4 || i === 5 || i === 6) {
            results.incomeTaxRate[i] = incomeTaxRate / 2; // 第5-7年所得税率=所得税率/2
        } else {
            results.incomeTaxRate[i] = incomeTaxRate; // 第N(N>=8)年所得税率=所得税率
        }
        
        // 计算折旧费
        // 第N年风机折旧费
        if (i === 0) {
            results.windTurbineDepreciation[i] = 0; // 第1年风机折旧费=0
        } else if (i >= 1 && i <= windTurbineDepreciationYears) {
            // 第N(2<=N<=固定资产折旧年限+1)年风机折旧费
            results.windTurbineDepreciation[i] = fixedWindTurbineAssetsNoTax * (1 - salvageRate) / windTurbineDepreciationYears;
        } else {
            results.windTurbineDepreciation[i] = 0;
        }
        
        // 第N年光伏折旧费
        if (i === 0) {
            results.photovoltaicDepreciation[i] = 0; // 第1年光伏折旧费=0
        } else if (i >= 1 && i <= photovoltaicDepreciationYears) {
            // 第N(2<=N<=固定资产折旧年限+1)年光伏折旧费
            results.photovoltaicDepreciation[i] = fixedPhotovoltaicAssetsNoTax * (1 - salvageRate) / photovoltaicDepreciationYears;
        } else {
            results.photovoltaicDepreciation[i] = 0;
        }
        
        // 第N年储能折旧费
        if (i === 0) {
            results.energyStorageDepreciation[i] = 0; // 第1年储能折旧费=0
        } else if (i >= 1 && i <= energyStorageDepreciationYears) {
            // 第N(2<=N<=固定资产折旧年限+1)年储能折旧费
            results.energyStorageDepreciation[i] = fixedEnergyStorageAssetsNoTax * (1 - salvageRate) / energyStorageDepreciationYears;
        } else {
            results.energyStorageDepreciation[i] = 0;
        }
        
        // 第N年电解槽折旧费
        if (i === 0) {
            results.electrolyzerDepreciation[i] = 0; // 第1年电解槽折旧费=0
        } else if (i >= 1 && i <= electrolyzerDepreciationYears) {
            // 第N(2<=N<=固定资产折旧年限+1)年电解槽折旧费
            results.electrolyzerDepreciation[i] = fixedElectrolyzerAssetsNoTax * (1 - salvageRate) / electrolyzerDepreciationYears;
        } else {
            results.electrolyzerDepreciation[i] = 0;
        }
        
        // 第N年储罐折旧费
        if (i === 0) {
            results.hydrogenStorageDepreciation[i] = 0; // 第1年储罐折旧费=0
        } else if (i >= 1 && i <= hydrogenStorageDepreciationYears) {
            // 第N(2<=N<=固定资产折旧年限+1)年储罐折旧费
            results.hydrogenStorageDepreciation[i] = fixedHydrogenStorageAssetsNoTax * (1 - salvageRate) / hydrogenStorageDepreciationYears;
        } else {
            results.hydrogenStorageDepreciation[i] = 0;
        }
        
        // 计算总值，保持原有变量名不变
        // 第N年发电折旧费 = 风机折旧费 + 光伏折旧费 + 储能折旧费
        results.powerDepreciation[i] = results.windTurbineDepreciation[i] + 
                                     results.photovoltaicDepreciation[i] + 
                                     results.energyStorageDepreciation[i];
        
        // 第N年制氢折旧费 = 电解槽折旧费 + 储罐折旧费
        results.hydrogenDepreciation[i] = results.electrolyzerDepreciation[i] + 
                                        results.hydrogenStorageDepreciation[i];
        
        // 第N年折旧费 = 第N年发电折旧费 + 第N年制氢折旧费
        results.depreciation[i] = results.powerDepreciation[i] + results.hydrogenDepreciation[i];
        
        // 计算维修费
        // 第N年风机维修费
        if (i === 0) {
            results.windTurbineMaintenanceCost[i] = 0; // 第一年风机维修费=0
        } else if (i === 1) {
            // 第2年风机维修费
            results.windTurbineMaintenanceCost[i] = windTurbineMaintCostRateYear2 * 
                                               windTurbineEquipmentAndInstallationWithTax * 
                                               powerGenerationCostSensitivityFactor;
        } else {
            // 根据年份确定增长率
            let growthRate = 0;
            if (i >= 2 && i <= 5) {
                growthRate = 0; // 第3到6年风机维修费率增长率=0
            } else if (i >= 6 && i <= 10) {
                growthRate = windTurbineMaintCostGrowthRateYears7to11; // 第7到11年风机维修费年增长率
            } else {
                growthRate = windTurbineMaintCostGrowthRateYears12plus; // 第12到N年风机维修费年增长率
            }
            
            // 第N(N>=3)年风机维修费
            results.windTurbineMaintenanceCost[i] = results.windTurbineMaintenanceCost[i-1] * 
                                               (1 + growthRate) * 
                                               powerGenerationCostSensitivityFactor;
        }
        
        // 第N年光伏维修费
        if (i === 0) {
            results.photovoltaicMaintenanceCost[i] = 0; // 第一年光伏维修费=0
        } else if (i === 1) {
            // 第2年光伏维修费
            results.photovoltaicMaintenanceCost[i] = photovoltaicMaintCostRateYear2 * 
                                                photovoltaicEquipmentAndInstallationWithTax * 
                                                powerGenerationCostSensitivityFactor;
        } else {
            // 根据年份确定增长率
            let growthRate = 0;
            if (i >= 2 && i <= 5) {
                growthRate = 0; // 第3到6年光伏维修费率增长率=0
            } else if (i >= 6 && i <= 10) {
                growthRate = photovoltaicMaintCostGrowthRateYears7to11; // 第7到11年光伏维修费年增长率
            } else {
                growthRate = photovoltaicMaintCostGrowthRateYears12plus; // 第12到N年光伏维修费年增长率
            }
            
            // 第N(N>=3)年光伏维修费
            results.photovoltaicMaintenanceCost[i] = results.photovoltaicMaintenanceCost[i-1] * 
                                                (1 + growthRate) * 
                                                powerGenerationCostSensitivityFactor;
        }
        
        // 第N年储能维修费
        if (i === 0) {
            results.energyStorageMaintenanceCost[i] = 0; // 第一年储能维修费=0
        } else if (i === 1) {
            // 第2年储能维修费
            results.energyStorageMaintenanceCost[i] = energyStorageMaintCostRateYear2 * 
                                                 energyStorageEquipmentAndInstallationWithTax * 
                                                 powerGenerationCostSensitivityFactor;
        } else {
            // 根据年份确定增长率
            let growthRate = 0;
            if (i >= 2 && i <= 5) {
                growthRate = 0; // 第3到6年储能维修费率增长率=0
            } else if (i >= 6 && i <= 10) {
                growthRate = energyStorageMaintCostGrowthRateYears7to11; // 第7到11年储能维修费年增长率
            } else {
                growthRate = energyStorageMaintCostGrowthRateYears12plus; // 第12到N年储能维修费年增长率
            }
            
            // 第N(N>=3)年储能维修费
            results.energyStorageMaintenanceCost[i] = results.energyStorageMaintenanceCost[i-1] * 
                                                 (1 + growthRate) * 
                                                 powerGenerationCostSensitivityFactor;
        }
        
        // 第N年电解槽维修费
        if (i === 0) {
            results.electrolyzerMaintenanceCost[i] = 0; // 第一年电解槽维修费=0
        } else if (i === 1) {
            // 第2年电解槽维修费
            results.electrolyzerMaintenanceCost[i] = electrolyzerMaintCostRateYear2 * 
                                                electrolyzerEquipmentAndInstallationWithTax * 
                                                hydrogenProductionCostSensitivityFactor;
        } else {
            // 根据年份确定增长率
            let growthRate = 0;
            if (i >= 2 && i <= 5) {
                growthRate = 0; // 第3到6年电解槽维修费率增长率=0
            } else if (i >= 6 && i <= 10) {
                growthRate = electrolyzerMaintCostGrowthRateYears7to11; // 第7到11年电解槽维修费年增长率
            } else {
                growthRate = electrolyzerMaintCostGrowthRateYears12plus; // 第12到N年电解槽维修费年增长率
            }
            
            // 第N(N>=3)年电解槽维修费
            results.electrolyzerMaintenanceCost[i] = results.electrolyzerMaintenanceCost[i-1] * 
                                                (1 + growthRate) * 
                                                hydrogenProductionCostSensitivityFactor;
        }
        
        // 第N年储罐维修费
        if (i === 0) {
            results.hydrogenStorageMaintenanceCost[i] = 0; // 第一年储罐维修费=0
        } else if (i === 1) {
            // 第2年储罐维修费
            results.hydrogenStorageMaintenanceCost[i] = hydrogenStorageMaintCostRateYear2 * 
                                                    hydrogenStorageEquipmentAndInstallationWithTax * 
                                                    hydrogenProductionCostSensitivityFactor;
        } else {
            // 根据年份确定增长率
            let growthRate = 0;
            if (i >= 2 && i <= 5) {
                growthRate = 0; // 第3到6年储罐维修费率增长率=0
            } else if (i >= 6 && i <= 10) {
                growthRate = hydrogenStorageMaintCostGrowthRateYears7to11; // 第7到11年储罐维修费年增长率
            } else {
                growthRate = hydrogenStorageMaintCostGrowthRateYears12plus; // 第12到N年储罐维修费年增长率
            }
            
            // 第N(N>=3)年储罐维修费
            results.hydrogenStorageMaintenanceCost[i] = results.hydrogenStorageMaintenanceCost[i-1] * 
                                                    (1 + growthRate) * 
                                                    hydrogenProductionCostSensitivityFactor;
        }
        
        // 计算总值，保持原有变量名不变
        // 第N年发电维修费 = 风机维修费 + 光伏维修费 + 储能维修费
        results.powerMaintenanceCost[i] = results.windTurbineMaintenanceCost[i] + 
                                        results.photovoltaicMaintenanceCost[i] + 
                                        results.energyStorageMaintenanceCost[i];
        
        // 第N年制氢维修费 = 电解槽维修费 + 储罐维修费
        results.hydrogenMaintenanceCost[i] = results.electrolyzerMaintenanceCost[i] + 
                                           results.hydrogenStorageMaintenanceCost[i];
        
        // 第N年维修费 = 第N年发电维修费 + 第N年制氢维修费
        results.maintenanceCost[i] = results.powerMaintenanceCost[i] + results.hydrogenMaintenanceCost[i];
        
        // 计算保险费
        // 第N年风机保险费
        if (i === 0) {
            results.windTurbineInsuranceCost[i] = 0; // 第1年风机保险费=0
        } else {
            // 第N(N>=2)年风机保险费
            results.windTurbineInsuranceCost[i] = fixedWindTurbineAssetsWithTax * 
                                             windTurbineInsuranceRate * 
                                             powerGenerationCostSensitivityFactor;
        }
        
        // 第N年光伏保险费
        if (i === 0) {
            results.photovoltaicInsuranceCost[i] = 0; // 第1年光伏保险费=0
        } else {
            // 第N(N>=2)年光伏保险费
            results.photovoltaicInsuranceCost[i] = fixedPhotovoltaicAssetsWithTax * 
                                              photovoltaicInsuranceRate * 
                                              powerGenerationCostSensitivityFactor;
        }
        
        // 第N年储能保险费
        if (i === 0) {
            results.energyStorageInsuranceCost[i] = 0; // 第1年储能保险费=0
        } else {
            // 第N(N>=2)年储能保险费
            results.energyStorageInsuranceCost[i] = fixedEnergyStorageAssetsWithTax * 
                                               energyStorageInsuranceRate * 
                                               powerGenerationCostSensitivityFactor;
        }
        
        // 第N年电解槽保险费
        if (i === 0) {
            results.electrolyzerInsuranceCost[i] = 0; // 第1年电解槽保险费=0
        } else {
            // 第N(N>=2)年电解槽保险费
            results.electrolyzerInsuranceCost[i] = fixedElectrolyzerAssetsWithTax * 
                                              electrolyzerInsuranceRate * 
                                              hydrogenProductionCostSensitivityFactor;
        }
        
        // 第N年储罐保险费
        if (i === 0) {
            results.hydrogenStorageInsuranceCost[i] = 0; // 第1年储罐保险费=0
        } else {
            // 第N(N>=2)年储罐保险费
            results.hydrogenStorageInsuranceCost[i] = fixedHydrogenStorageAssetsWithTax * 
                                                  hydrogenStorageInsuranceRate * 
                                                  hydrogenProductionCostSensitivityFactor;
        }
        
        // 计算总值，保持原有变量名不变
        // 第N年发电保险费 = 风机保险费 + 光伏保险费 + 储能保险费
        results.powerInsuranceCost[i] = results.windTurbineInsuranceCost[i] + 
                                      results.photovoltaicInsuranceCost[i] + 
                                      results.energyStorageInsuranceCost[i];
        
        // 第N年制氢保险费 = 电解槽保险费 + 储罐保险费
        results.hydrogenInsuranceCost[i] = results.electrolyzerInsuranceCost[i] + 
                                         results.hydrogenStorageInsuranceCost[i];
        
        // 第N年保险费 = 第N年发电保险费 + 第N年制氢保险费
        results.insuranceCost[i] = results.powerInsuranceCost[i] + results.hydrogenInsuranceCost[i];
        
        // 计算水费 - 只有电解槽用水
        // 第N年电解槽水费
        if (i === 0) {
            results.electrolyzerWaterCost[i] = 0; // 第1年电解槽水费=0
        } else {
            // 第N(N>=2)年电解槽水费 = 第N年耗水量 * 水费单价不含税
            results.electrolyzerWaterCost[i] = waterConsumption[i] * waterPriceNoTax;
        }
        
        // 设置总值，保持原有变量名不变
        // 第N年制氢水费 = 电解槽水费
        results.hydrogenWaterCost[i] = results.electrolyzerWaterCost[i];
        
        // 第N年水费 = 第N年制氢水费
        results.waterCost[i] = results.hydrogenWaterCost[i];
        
        // 计算电费 - 只有电解槽用电
        // 第N年电解槽电费
        if (i === 0) {
            results.electrolyzerElectricityCost[i] = 0; // 第1年电解槽电费=0
        } else {
            // 第N(N>=2)年电解槽电费 = 第N年绿电制氢电量 * 自备电厂不含税电价 + 第N年下网制氢电量 * 下网不含税电价
            results.electrolyzerElectricityCost[i] = greenHydrogenElectricity[i] * selfPowerPlantElectricityPriceNoTax + 
                                                 gridHydrogenElectricity[i] * gridHydrogenElectricityPriceNoTax;
        }
        // 公辅装置电费
        if (i ===0) {
            results.publicLoadPowerCost[i] = 0
        } else {
            results.publicLoadPowerCost[i] = publicLoadCapacity * publicLoadHour * gridHydrogenElectricityPriceNoTax / 10
        }
        
        // 设置总值，保持原有变量名不变
        // 第N年制氢电费 = 电解槽电费
        results.hydrogenElectricityCost[i] = results.electrolyzerElectricityCost[i];
        
        // 第N年电费 = 第N年制氢电费
        results.electricityCost[i] = results.hydrogenElectricityCost[i];
        
        // 计算材料费
        // 第N年风机材料费
        if (i === 0) {
            results.windTurbineMaterialsCost[i] = 0; // 第1年风机材料费=0
        } else if (i === 1) {
            // 第2年风机材料费
            results.windTurbineMaterialsCost[i] = windTurbineEquipmentAndInstallationNoTax * 
                                             windTurbineMaterialsCostProvisionRate * 
                                             totalCostSensitivityFactor;
        } else {
            // 第N(N>=3)年风机材料费
            results.windTurbineMaterialsCost[i] = results.windTurbineMaterialsCost[i-1] * 
                                             (1 + windTurbineMaterialsCostAnnualGrowthRate) * 
                                             totalCostSensitivityFactor;
        }
        
        // 第N年光伏材料费
        if (i === 0) {
            results.photovoltaicMaterialsCost[i] = 0; // 第1年光伏材料费=0
        } else if (i === 1) {
            // 第2年光伏材料费
            results.photovoltaicMaterialsCost[i] = photovoltaicEquipmentAndInstallationNoTax * 
                                              photovoltaicMaterialsCostProvisionRate * 
                                              totalCostSensitivityFactor;
        } else {
            // 第N(N>=3)年光伏材料费
            results.photovoltaicMaterialsCost[i] = results.photovoltaicMaterialsCost[i-1] * 
                                              (1 + photovoltaicMaterialsCostAnnualGrowthRate) * 
                                              totalCostSensitivityFactor;
        }
        
        // 第N年储能材料费
        if (i === 0) {
            results.energyStorageMaterialsCost[i] = 0; // 第1年储能材料费=0
        } else if (i === 1) {
            // 第2年储能材料费
            results.energyStorageMaterialsCost[i] = energyStorageEquipmentAndInstallationNoTax * 
                                               energyStorageMaterialsCostProvisionRate * 
                                               totalCostSensitivityFactor;
        } else {
            // 第N(N>=3)年储能材料费
            results.energyStorageMaterialsCost[i] = results.energyStorageMaterialsCost[i-1] * 
                                               (1 + energyStorageMaterialsCostAnnualGrowthRate) * 
                                               totalCostSensitivityFactor;
        }
        
        // 第N年电解槽材料费
        if (i === 0) {
            results.electrolyzerMaterialsCost[i] = 0; // 第1年电解槽材料费=0
        } else if (i === 1) {
            // 第2年电解槽材料费
            results.electrolyzerMaterialsCost[i] = electrolyzerEquipmentAndInstallationNoTax * 
                                              electrolyzerMaterialsCostProvisionRate * 
                                              totalCostSensitivityFactor;
        } else {
            // 第N(N>=3)年电解槽材料费
            results.electrolyzerMaterialsCost[i] = results.electrolyzerMaterialsCost[i-1] * 
                                              (1 + electrolyzerMaterialsCostAnnualGrowthRate) * 
                                              totalCostSensitivityFactor;
        }
        
        // 第N年储罐材料费
        if (i === 0) {
            results.hydrogenStorageMaterialsCost[i] = 0; // 第1年储罐材料费=0
        } else if (i === 1) {
            // 第2年储罐材料费
            results.hydrogenStorageMaterialsCost[i] = hydrogenStorageEquipmentAndInstallationNoTax * 
                                                 hydrogenStorageMaterialsCostProvisionRate * 
                                                 totalCostSensitivityFactor;
        } else {
            // 第N(N>=3)年储罐材料费
            results.hydrogenStorageMaterialsCost[i] = results.hydrogenStorageMaterialsCost[i-1] * 
                                                 (1 + hydrogenStorageMaterialsCostAnnualGrowthRate) * 
                                                 totalCostSensitivityFactor;
        }
        
        // 计算总材料费
        // 第N年材料费 = 所有组件材料费之和
        if (i === 0) {
            results.materialsCost[i] = 0; // 第1年材料费=0
        } else {
            results.materialsCost[i] = results.windTurbineMaterialsCost[i] + 
                                     results.photovoltaicMaterialsCost[i] + 
                                     results.energyStorageMaterialsCost[i] + 
                                     results.electrolyzerMaterialsCost[i] + 
                                     results.hydrogenStorageMaterialsCost[i];
        }
        
        // 计算运输成本 - 只有电解槽产品需要运输
        // 第N年电解槽产品运输成本
        if (i === 0) {
            results.electrolyzerTransportationCost[i] = 0; // 第1年电解槽产品运输成本=0
        } else {
            // 第N年电解槽产品运输成本 = 运输费率不含税 * 第N年制氢量 * 运输距离
            results.electrolyzerTransportationCost[i] = transportationRateNoTax * 
                                                   hydrogenProduction[i] * 
                                                   transportDistance;
        }
        
        // 设置总值，保持原有变量名不变
        // 第N年氢气运输成本 = 电解槽产品运输成本
        results.hydrogenTransportationCost[i] = results.electrolyzerTransportationCost[i];
        
        // 第N年运输成本 = 第N年氢气运输成本
        results.transportationCost[i] = results.hydrogenTransportationCost[i];
        
        // 计算设备大修更换费用
        // 第N年风机设备大修更换费用
        if (i >= 0 && i <= 9) {
            results.windTurbineOverhaulReplacementCost[i] = 0; // 第N(1<=N<=10)年风机设备大修更换费用=0
        } else if (i === 10) {
            // 第11年风机设备大修更换费用
            results.windTurbineOverhaulReplacementCost[i] = windTurbineEquipmentAndInstallationNoTax * windTurbineMajorOverhaulReplacementRate;
        } else if (i >= 11 && i <= 19) {
            results.windTurbineOverhaulReplacementCost[i] = 0; // 第N(12<=N<=20年)年风机设备大修更换费用=0
        } else if (i === 20) {
            // 第21年风机设备大修更换费用
            results.windTurbineOverhaulReplacementCost[i] = windTurbineEquipmentAndInstallationNoTax * windTurbineMajorOverhaulReplacementRate;
        } else {
            results.windTurbineOverhaulReplacementCost[i] = 0; // 第N(N>=22)年风机设备大修更换费用=0
        }
        
        // 第N年光伏设备大修更换费用
        if (i >= 0 && i <= 9) {
            results.photovoltaicOverhaulReplacementCost[i] = 0; // 第N(1<=N<=10)年光伏设备大修更换费用=0
        } else if (i === 10) {
            // 第11年光伏设备大修更换费用
            results.photovoltaicOverhaulReplacementCost[i] = photovoltaicEquipmentAndInstallationNoTax * photovoltaicMajorOverhaulReplacementRate;
        } else if (i >= 11 && i <= 19) {
            results.photovoltaicOverhaulReplacementCost[i] = 0; // 第N(12<=N<=20年)年光伏设备大修更换费用=0
        } else if (i === 20) {
            // 第21年光伏设备大修更换费用
            results.photovoltaicOverhaulReplacementCost[i] = photovoltaicEquipmentAndInstallationNoTax * photovoltaicMajorOverhaulReplacementRate;
        } else {
            results.photovoltaicOverhaulReplacementCost[i] = 0; // 第N(N>=22)年光伏设备大修更换费用=0
        }
        
        // 第N年储能设备大修更换费用
        if (i >= 0 && i <= 9) {
            results.energyStorageOverhaulReplacementCost[i] = 0; // 第N(1<=N<=10)年储能设备大修更换费用=0
        } else if (i === 10) {
            // 第11年储能设备大修更换费用
            results.energyStorageOverhaulReplacementCost[i] = energyStorageEquipmentAndInstallationNoTax * energyStorageMajorOverhaulReplacementRate;
        } else if (i >= 11 && i <= 19) {
            results.energyStorageOverhaulReplacementCost[i] = 0; // 第N(12<=N<=20年)年储能设备大修更换费用=0
        } else if (i === 20) {
            // 第21年储能设备大修更换费用
            results.energyStorageOverhaulReplacementCost[i] = energyStorageEquipmentAndInstallationNoTax * energyStorageMajorOverhaulReplacementRate;
        } else {
            results.energyStorageOverhaulReplacementCost[i] = 0; // 第N(N>=22)年储能设备大修更换费用=0
        }
        
        // 第N年电解槽设备大修更换费用
        if (i >= 0 && i <= 9) {
            results.electrolyzerOverhaulReplacementCost[i] = 0; // 第N(1<=N<=10)年电解槽设备大修更换费用=0
        } else if (i === 10) {
            // 第11年电解槽设备大修更换费用
            results.electrolyzerOverhaulReplacementCost[i] = electrolyzerEquipmentAndInstallationNoTax * electrolyzerMajorOverhaulReplacementRate;
        } else if (i >= 11 && i <= 19) {
            results.electrolyzerOverhaulReplacementCost[i] = 0; // 第N(12<=N<=20年)年电解槽设备大修更换费用=0
        } else if (i === 20) {
            // 第21年电解槽设备大修更换费用
            results.electrolyzerOverhaulReplacementCost[i] = electrolyzerEquipmentAndInstallationNoTax * electrolyzerMajorOverhaulReplacementRate;
        } else {
            results.electrolyzerOverhaulReplacementCost[i] = 0; // 第N(N>=22)年电解槽设备大修更换费用=0
        }
        
        // 第N年储罐设备大修更换费用
        if (i >= 0 && i <= 9) {
            results.hydrogenStorageOverhaulReplacementCost[i] = 0; // 第N(1<=N<=10)年储罐设备大修更换费用=0
        } else if (i === 10) {
            // 第11年储罐设备大修更换费用
            results.hydrogenStorageOverhaulReplacementCost[i] = hydrogenStorageEquipmentAndInstallationNoTax * hydrogenStorageMajorOverhaulReplacementRate;
        } else if (i >= 11 && i <= 19) {
            results.hydrogenStorageOverhaulReplacementCost[i] = 0; // 第N(12<=N<=20年)年储罐设备大修更换费用=0
        } else if (i === 20) {
            // 第21年储罐设备大修更换费用
            results.hydrogenStorageOverhaulReplacementCost[i] = hydrogenStorageEquipmentAndInstallationNoTax * hydrogenStorageMajorOverhaulReplacementRate;
        } else {
            results.hydrogenStorageOverhaulReplacementCost[i] = 0; // 第N(N>=22)年储罐设备大修更换费用=0
        }
        
        // 计算总设备大修更换费用
        // 第N年设备大修更换费用 = 所有组件设备大修更换费用之和
        results.equipmentOverhaulReplacementCost[i] = results.windTurbineOverhaulReplacementCost[i] + 
                                                    results.photovoltaicOverhaulReplacementCost[i] + 
                                                    results.energyStorageOverhaulReplacementCost[i] + 
                                                    results.electrolyzerOverhaulReplacementCost[i] + 
                                                    results.hydrogenStorageOverhaulReplacementCost[i];
        
        // 计算土地租赁费 - 只有风机和光伏需要土地费用
        // 第N年风机土地租赁费
        // if (i === 0) {
        //     results.windTurbineLandRentalFee[i] = 0; // 第1年风机土地租赁费=0
        // } else if (i === 1) {
        //     results.windTurbineLandRentalFee[i] = windTurbineLandRentalFeeYear2; // 第2年风机土地租赁费=默认值
        // } else {
        //     // 第N(N>=3)年风机土地租赁费
        //     results.windTurbineLandRentalFee[i] = results.windTurbineLandRentalFee[i-1] * (1 + windTurbineLandRentalFeeGrowthRate);
        // }
        
        // 第N年光伏土地租赁费
        if (i === 0) {
            results.photovoltaicLandRentalFee[i] = 0; // 第1年光伏土地租赁费=0
        } else if (i === 1) {
            results.photovoltaicLandRentalFee[i] = photovoltaicLandRentalFeeYear2; // 第2年光伏土地租赁费=默认值
        } else {
            // 第N(N>=3)年光伏土地租赁费
            results.photovoltaicLandRentalFee[i] = results.photovoltaicLandRentalFee[i-1] * (1 + photovoltaicLandRentalFeeGrowthRate);
        }
        
        // 计算总土地租赁费
        // 第N年土地租赁费 = 风机土地租赁费 + 光伏土地租赁费
        // results.landRentalFee[i] = results.windTurbineLandRentalFee[i] + results.photovoltaicLandRentalFee[i];
        results.landRentalFee[i] = results.photovoltaicLandRentalFee[i]; 
        
        // 第N年扶贫规费支出
        results.povertyAlleviationFee[i] = 0; // 默认值为0
        
        // 计算土地税费 - 只有风机和光伏需要土地税费
        // 第N年风机土地税费
        if (i === 0) {
            results.windTurbineLandTaxFee[i] = 0; // 第1年风机土地税费=0
        } else {
            results.windTurbineLandTaxFee[i] = windTurbineLandTaxAnnual; // 默认值
        }
        
        // 第N年光伏土地税费
        if (i === 0) {
            results.photovoltaicLandTaxFee[i] = 0; // 第1年光伏土地税费=0
        } else {
            results.photovoltaicLandTaxFee[i] = photovoltaicLandTaxAnnual; // 默认值
        }
        
        // 计算总土地税费
        // 第N年土地税费 = 风机土地税费 + 光伏土地税费
        results.landTaxFee[i] = results.windTurbineLandTaxFee[i] + results.photovoltaicLandTaxFee[i];
        
        // TODO: 计算其他费用, 考虑各模块容量
        // 第N年风机其他费用
        if (i === 0) {
            results.windTurbineOtherCost[i] = 0; // 第1年风机其他费用=0
        } else if (i === 1) {
            // 第2年风机其他费用
            results.windTurbineOtherCost[i] = windTurbineOtherCostUnitPrice * 
                                          windCapacity / 10 * 
                                         powerGenerationCostSensitivityFactor;
        } else {
            // 第N(N>=3)年风机其他费用
            results.windTurbineOtherCost[i] = results.windTurbineOtherCost[i-1] * 
                                         (1 + windTurbineOtherCostGrowthRate) * 
                                         powerGenerationCostSensitivityFactor;
        }
        
        // 第N年光伏其他费用
        if (i === 0) {
            results.photovoltaicOtherCost[i] = 0; // 第1年光伏其他费用=0
        } else if (i === 1) {
            // 第2年光伏其他费用
            results.photovoltaicOtherCost[i] = photovoltaicOtherCostUnitPrice * 
                                          pvCapacity / 10 * 
                                          powerGenerationCostSensitivityFactor;
        } else {
            // 第N(N>=3)年光伏其他费用
            results.photovoltaicOtherCost[i] = results.photovoltaicOtherCost[i-1] * 
                                          (1 + photovoltaicOtherCostGrowthRate) * 
                                          powerGenerationCostSensitivityFactor;
        }
        
        // 第N年储能其他费用
        if (i === 0) {
            results.energyStorageOtherCost[i] = 0; // 第1年储能其他费用=0
        } else if (i === 1) {
            // 第2年储能其他费用
            results.energyStorageOtherCost[i] = energyStorageOtherCostUnitPrice * 
                                           batCapacity / 10 * 
                                           powerGenerationCostSensitivityFactor;
        } else {
            // 第N(N>=3)年储能其他费用
            results.energyStorageOtherCost[i] = results.energyStorageOtherCost[i-1] * 
                                           (1 + energyStorageOtherCostGrowthRate) * 
                                           powerGenerationCostSensitivityFactor;
        }
        
        // 第N年电解槽其他费用
        if (i === 0) {
            results.electrolyzerOtherCost[i] = 0; // 第1年电解槽其他费用=0
        } else if (i === 1) {
            // 第2年电解槽其他费用
            results.electrolyzerOtherCost[i] = electrolyzerOtherCostUnitPrice * 
                                          alkCapacity / 10 * 
                                          hydrogenProductionCostSensitivityFactor;
        } else {
            // 第N(N>=3)年电解槽其他费用
            results.electrolyzerOtherCost[i] = results.electrolyzerOtherCost[i-1] * 
                                          (1 + electrolyzerOtherCostGrowthRate) * 
                                          hydrogenProductionCostSensitivityFactor;
        }
        
        // 第N年储罐其他费用 TODO: 储罐容量
        if (i === 0) {
            results.hydrogenStorageOtherCost[i] = 0; // 第1年储罐其他费用=0
        } else if (i === 1) {
            // 第2年储罐其他费用
            results.hydrogenStorageOtherCost[i] = hydrogenStorageOtherCostUnitPrice * 
                                             alkStoreCapacity / 10 * 
                                             hydrogenProductionCostSensitivityFactor;
        } else {
            // 第N(N>=3)年储罐其他费用
            results.hydrogenStorageOtherCost[i] = results.hydrogenStorageOtherCost[i-1] * 
                                             (1 + hydrogenStorageOtherCostGrowthRate) * 
                                             hydrogenProductionCostSensitivityFactor;
        }
        
        // 计算发电其他费用 = 风机 + 光伏 + 储能
        results.powerOtherCost[i] = results.windTurbineOtherCost[i] + 
                                  results.photovoltaicOtherCost[i] + 
                                  results.energyStorageOtherCost[i];
        
        // 计算制氢其他费用 = 电解槽 + 储罐
        results.hydrogenOtherCost[i] = results.electrolyzerOtherCost[i] + 
                                     results.hydrogenStorageOtherCost[i];
        
        // 第N年其他费用 = 第N年制氢其他费用 + 第N年发电其他费用
        results.otherCost[i] = results.hydrogenOtherCost[i] + results.powerOtherCost[i];
        
        // 计算水资源税 - 只有电解槽需要水资源税
        if (i === 0) {
            results.electrolyzerWaterResourceTax[i] = 0; // 第1年电解槽水资源税=0
            results.waterResourceTax[i] = 0; // 第1年水资源税=0
        } else {
            // 第N年电解槽水资源税 = 第N年耗水量(单位：万吨) * 水资源税单价(单位：元/吨)
            results.electrolyzerWaterResourceTax[i] = waterConsumption[i] * waterResourceTaxUnitPrice;
            
            // 第N年水资源税 = 第N年电解槽水资源税
            results.waterResourceTax[i] = results.electrolyzerWaterResourceTax[i];
        }
        
        // 计算工资福利及劳保统筹和住房基金
        if (i === 0) {
            results.laborCost[i] = 0; // 第1年工资福利及劳保统筹和住房基金=0
        } else if (i === 1) {
            // 人员薪资(单位：万元) = 工人数量 * 工人人均月薪 + 技术人员数量 * 技术人均月薪 + 管理人员数量 * 管理人均月薪
            const salary = (workerCount * workerMonthlySalary + 
                         technicianCount * technicianMonthlySalary + 
                         managerCount * managerMonthlySalary) * 12; // 乘以12转为年薪
            
            // 福利社保 = 人员薪资 * 福利社保系数
            const welfareSecurity = salary * socialSecurityAndWelfareFactor;
            
            // 第2年工资福利及劳保统筹和住房基金 = (薪资 + 福利社保) * 总成本敏感系数
            results.laborCost[i] = (salary + welfareSecurity) * totalCostSensitivityFactor;
        } else {
            // 第N(N>2)年工资福利及劳保统筹和住房基金 = 第N-1年工资福利及劳保统筹和住房基金 * (1 + 薪资年增长率) * 总成本敏感系数
            results.laborCost[i] = results.laborCost[i-1] * 
                               (1 + salaryAnnualGrowthRate) * 
                               totalCostSensitivityFactor;
        }
        
        // 第N年摊销费
        results.amortizationCost[i] = 0; // 默认值为0
        
        // 计算进项税
        // 第N年维修费进项税
        if (i === 0) {
            results.maintenanceCostInputTax[i] = 0; // 第1年维修费进项税=0
        } else {
            // 第N(N>=2)年维修费进项税 = 第N年维修费 * 维修费增值税率
            results.maintenanceCostInputTax[i] = results.maintenanceCost[i] * maintenanceCostVATRate;
        }
        
        // 第N年材料成本进项税
        if (i === 0) {
            results.materialsCostInputTax[i] = 0; // 第1年材料成本进项税=0
        } else {
            // 第N(N>=2)年材料成本进项税 = 第N年材料费 * 材料费增值税率
            results.materialsCostInputTax[i] = results.materialsCost[i] * materialsCostVATRate;
        }
        
        // 第N年运输成本进项税
        if (i === 0) {
            results.transportationCostInputTax[i] = 0; // 第1年运输成本进项税=0
        } else {
            // 第N(N>=2)年运输成本进项税 = 第N年运输成本 * 运输费增值税率
            results.transportationCostInputTax[i] = results.transportationCost[i] * transportationFeeVATRate;
        }
        
        // 第N年水费进项税
        if (i === 0) {
            results.waterCostInputTax[i] = 0; // 第1年水费进项税=0
        } else {
            // 第N(N>=2)年水费进项税 = 第N年水费 * 水费增值税率
            results.waterCostInputTax[i] = results.waterCost[i] * waterFeeVATRate;
        }
        
        // 第N年电费进项税
        if (i === 0) {
            results.electricityCostInputTax[i] = 0; // 第1年电费进项税=0
        } else {
            // 第N(N>=2)年电费进项税 = 第N年电费 * 电费增值税率
            results.electricityCostInputTax[i] = results.electricityCost[i] * electricityFeeVATRate;
        }
        
        // 第N年进项税
        if (i === 0) {
            results.inputTax[i] = 0; // 第1年进项税=0
        } else {
            // 第N(N>=2)年进项税 = 第N年维修费进项税 + 第N年材料成本进项税 + 第N年运输成本进项税 + 第N年水费进项税 + 第N年电费进项税
            results.inputTax[i] = results.maintenanceCostInputTax[i] + 
                              results.materialsCostInputTax[i] + 
                              results.transportationCostInputTax[i] + 
                              results.waterCostInputTax[i] + 
                              results.electricityCostInputTax[i];
        }
        
        // 计算含税利息支出
        // 第N年含税利息支出 = 第N年本年付息 + 第N年短期借款利息 + 第N年流动资金利息
        if (i === 0) {
            results.interestExpenseWithTax[i] = 0; // 第1年含税利息支出=0 (固定值)
        } else {
            results.interestExpenseWithTax[i] = annualInterest[i] + shortTermLoanInterest[i] + workingCapitalInterest[i];
        }

        // expand
        initInfo?.totalCost?.length && initInfo?.totalCost?.forEach((v) => {
            if (v.key && v.value?.length) {
                results[v.key][i] = v.value[i]
                // console.log('v:', v.key, v.value)
            }
        })
        
        // 第N年固定成本 = 第N年折旧费 + 第N年电费 + 第N年含税利息支出 + 第N年设备大修更换费用 + 第N年土地租赁费 + 第N年扶贫规费支出 + 第N年土地税费
        results.fixedCost[i] = results.depreciation[i] + 
                           results.electricityCost[i] + 
                           results.interestExpenseWithTax[i] + 
                           results.equipmentOverhaulReplacementCost[i] + 
                           results.landRentalFee[i] + 
                           results.povertyAlleviationFee[i] + 
                           results.landTaxFee[i] + 
                           results.publicLoadPowerCost[i];
        // // TODO:固定成本-发电
        // results.fixedFaDianCost[i] = results.powerDepreciation[i]
        //     + 0
        //     + results.windTurbineOverhaulReplacementCost[i] + results.photovoltaicOverhaulReplacementCost[i] + results.energyStorageOverhaulReplacementCost[i]

        // // TODO: 固定成本-制氢
        // results.fixedFaDianCost[i] = results.hydrogenDepreciation[i] +
        //     + results.hydrogenElectricityCost[i]
        //     + results.electrolyzerOverhaulReplacementCost[i] + results.hydrogenStorageOverhaulReplacementCost[i]
        
        // 第N年可变成本
        if (i === 0) {
            results.variableCost[i] = 0; // 第1年可变成本=0
        } else {
            // 第N年可变成本 = 第N年维修费 + 第N年工资福利及劳保统筹和住房基金 + 第N年保险费 + 第N年水费 + 第N年材料费 + 第N年运输成本 + 第N年其他费用 + 第N年水资源税
            results.variableCost[i] = results.maintenanceCost[i] + 
                                  results.laborCost[i] + 
                                  results.insuranceCost[i] + 
                                  results.waterCost[i] + 
                                  results.materialsCost[i] + 
                                  results.transportationCost[i] + 
                                  results.otherCost[i] + 
                                  results.waterResourceTax[i];
        }
        
        // 第N年总成本费用 = 第N年固定成本 + 第N年可变成本
        results.totalCostExpense[i] = results.fixedCost[i] + results.variableCost[i];
    }

    // 为了更好的可读性，将输出分为多个表格
    if (showLogs) {
        console.log("\n===== 总成本费用表 =====");
        
        // 1. 显示折旧相关费用
        const depreciationResults = {};
        for (let i = 0; i < years; i++) {
            depreciationResults[`第${i}年`] = {
                "所得税率": results.incomeTaxRate[i],
                "折旧费": results.depreciation[i],
                "发电折旧费": results.powerDepreciation[i],
                "制氢折旧费": results.hydrogenDepreciation[i]
            };
        }
        console.log("1. 折旧费用");
        console.table(depreciationResults);
        
        // 2. 显示维修相关费用
        const maintenanceResults = {};
        for (let i = 0; i < years; i++) {
            maintenanceResults[`第${i}年`] = {
                "维修费": results.maintenanceCost[i],
                "发电维修费": results.powerMaintenanceCost[i],
                "制氢维修费": results.hydrogenMaintenanceCost[i]
            };
        }
        console.log("\n2. 维修费用");
        console.table(maintenanceResults);
        
        // 3. 显示保险相关费用
        const insuranceResults = {};
        for (let i = 0; i < years; i++) {
            insuranceResults[`第${i}年`] = {
                "保险费": results.insuranceCost[i],
                "发电保险费": results.powerInsuranceCost[i],
                "制氢保险费": results.hydrogenInsuranceCost[i]
            };
        }
        console.log("\n3. 保险费用");
        console.table(insuranceResults);
        
        // 4. 显示水电相关费用
        const waterAndElectricityResults = {};
        for (let i = 0; i < years; i++) {
            waterAndElectricityResults[`第${i}年`] = {
                "水费": results.waterCost[i],
                "制氢水费": results.hydrogenWaterCost[i],
                "电费": results.electricityCost[i],
                "制氢电费": results.hydrogenElectricityCost[i],
                "水资源税": results.waterResourceTax[i],
                "公辅装置电费": results.publicLoadPowerCost[i]
            };
        }
        console.log("\n4. 水电费用");
        console.table(waterAndElectricityResults);
        
        // 5. 显示材料和运输相关费用
        const materialsAndTransportationResults = {};
        for (let i = 0; i < years; i++) {
            materialsAndTransportationResults[`第${i}年`] = {
                "材料费": results.materialsCost[i],
                "运输成本": results.transportationCost[i],
                "氢气运输成本": results.hydrogenTransportationCost[i]
            };
        }
        console.log("\n5. 材料和运输费用");
        console.table(materialsAndTransportationResults);
        
        // 6. 显示土地和其他相关费用
        const landAndOtherResults = {};
        for (let i = 0; i < years; i++) {
            landAndOtherResults[`第${i}年`] = {
                "设备大修更换费用": results.equipmentOverhaulReplacementCost[i],
                "土地租赁费": results.landRentalFee[i],
                "扶贫规费支出": results.povertyAlleviationFee[i],
                "土地税费": results.landTaxFee[i],
                "其他费用": results.otherCost[i],
                "发电其他费用": results.powerOtherCost[i],
                "制氢其他费用": results.hydrogenOtherCost[i]
            };
        }
        console.log("\n6. 土地和其他费用");
        console.table(landAndOtherResults);
        
        // 7. 显示人工及摊销费用
        const laborAndAmortizationResults = {};
        for (let i = 0; i < years; i++) {
            laborAndAmortizationResults[`第${i}年`] = {
                "工资福利及劳保统筹和住房基金": results.laborCost[i],
                "摊销费": results.amortizationCost[i]
            };
        }
        console.log("\n7. 人工及摊销费用");
        console.table(laborAndAmortizationResults);
        
        // 8. 显示税费相关
        const taxResults = {};
        for (let i = 0; i < years; i++) {
            taxResults[`第${i}年`] = {
                "维修费进项税": results.maintenanceCostInputTax[i],
                "材料成本进项税": results.materialsCostInputTax[i],
                "运输成本进项税": results.transportationCostInputTax[i],
                "水费进项税": results.waterCostInputTax[i],
                "电费进项税": results.electricityCostInputTax[i],
                "进项税": results.inputTax[i]
            };
        }
        console.log("\n8. 税费相关");
        console.table(taxResults);
        
        // 9. 显示总成本费用
        const totalCostResults = {};
        for (let i = 0; i < years; i++) {
            totalCostResults[`第${i}年`] = {
                "含税利息支出": results.interestExpenseWithTax[i],
                "固定成本": results.fixedCost[i],
                "可变成本": results.variableCost[i],
                "总成本费用": results.totalCostExpense[i]
            };
        }
        console.log("\n9. 总成本费用");
        console.table(totalCostResults);
    }

    return results;
}

module.exports = { calculateTotalCostAndExpenses }; 