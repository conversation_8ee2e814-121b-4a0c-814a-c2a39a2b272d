const path = require('path');
const TerserPlugin = require('terser-webpack-plugin');

const isProduction = process.env.NODE_ENV === 'production';

module.exports = {
  target: 'node',
  context: path.resolve(__dirname, '..'),
  entry: {
    'finance-model': './finance_model/index.js',
    'run': './finance_model/run.js'
  },
  
  output: {
    path: path.resolve(__dirname, '../dist'),
    filename: '[name].js',
    library: {
      type: 'commonjs2'
    },
    clean: true
  },
  
  externals: {
    // 将这些包标记为外部依赖，不打包进bundle
    'lodash': 'lodash',
    'dayjs': 'dayjs',
    'mathjs': 'mathjs'
  },
  
  resolve: {
    extensions: ['.js', '.json'],
    modules: ['node_modules']
  },
  
  optimization: {
    minimize: isProduction,
    minimizer: [
      new TerserPlugin({
        terserOptions: {
          compress: {
            drop_console: isProduction,
            drop_debugger: true,
            pure_funcs: isProduction ? ['console.log', 'console.warn', 'console.time', 'console.timeEnd'] : [],
            dead_code: true,
            conditionals: true,
            evaluate: true,
            booleans: true,
            loops: true,
            unused: true,
            hoist_funs: true,
            keep_fargs: false,
            hoist_vars: false,
            if_return: true,
            join_vars: true,
            side_effects: false,
            warnings: false,
            passes: 2,
            global_defs: isProduction ? {
              'showLogs': false
            } : {}
          },
          mangle: {
            reserved: ['runFinancialModel', 'getFinanceData'],
            toplevel: true,
            safari10: true
          },
          format: {
            comments: false,
            beautify: false,
            semicolons: true,
            ascii_only: true
          }
        },
        extractComments: false,
        parallel: true
      })
    ],
    splitChunks: false // 不拆分chunks，保持单文件输出
  },
  
  module: {
    rules: [
      {
        test: /\.js$/,
        exclude: /node_modules/,
        use: {
          loader: 'babel-loader',
          options: {
            presets: [
              ['@babel/preset-env', {
                targets: {
                  node: '14'
                }
              }]
            ]
          }
        }
      }
    ]
  },
  
  stats: {
    colors: true,
    modules: false,
    children: false,
    chunks: false,
    chunkModules: false
  },
  
  devtool: isProduction ? false : 'source-map'
}; 