/**
 * 计算还本付息计算表
 * @param {Object} params - 输入参数
 * @param {boolean} showLogs - 是否显示表格数据（可选，默认为false）
 * @returns {Object} - 还本付息计算表结果
 */
function calculateLoanRepaymentSchedule(params, showLogs = false) {
    const {
        longTermLoan, // 长期借款
        loanRate, // 借款利率 - 从关键指标模块获取
        loanTerm, // 贷款期限
        operatingYears // 项目运营年限
    } = params;

    // 借款利率检查 - 借款利率应该从关键指标(0-keyIndicators.js)传入
    if (typeof loanRate !== 'number' || isNaN(loanRate)) {
        console.error('错误: 借款利率未正确传入。请确保从关键指标模块获取正确的借款利率值。');
    }

    // 创建结果对象，包括所有年份的数据
    const years = operatingYears + 1; // +1 是因为包括第1年(建设期)
    const results = {
        longTermLoan: new Array(years).fill(0), // 第N年长期借款
        beginningLoanBalance: new Array(years).fill(0), // 第N年年初借款余额
        annualRepayment: new Array(years).fill(0), // 第N年本年还本
        annualInterest: new Array(years).fill(0), // 第N年本年付息
        endingLoanBalance: new Array(years).fill(0), // 第N年期末借款余额
        currentRepaymentAndInterest: new Array(years).fill(0), // 第N年当期还本付息
        workingCapitalLoanRepayment: new Array(years).fill(0), // 第N年偿还流动资金借款本金
        shortTermLoanRepayment: new Array(years).fill(0), // 第N年偿还短期借款本金
        workingCapitalInterest: new Array(years).fill(0), // 第N年流动资金利息
        shortTermLoanInterest: new Array(years).fill(0), // 第N年短期借款利息
        shortTermLoan: new Array(years).fill(0), // 第N年短期借款
        loanPrincipalRepayment: new Array(years).fill(0), // 第N年偿还借款本金
        workingCapitalLoan: new Array(years).fill(0), // 第N年流动资金借款
        loanInterestRepayment: new Array(years).fill(0), // 第N年偿还借款利息
        totalLoanPayment: new Array(years).fill(0) // 第N年借款本息合计
    };

    // 确保数值是有效的数字，否则返回默认值
    const ensureNumber = (value, defaultValue = 0) => {
        return (typeof value === 'number' && !isNaN(value) && isFinite(value)) ? value : defaultValue;
    };

    // 计算各年数据
    for (let i = 0; i < years; i++) {
        // 注意：i对应的是数组索引，实际年份是i+1
        // 第1年(i=0): 建设期
        // 第2年及以后(i>=1): 运营期

        // 第N年长期借款 - 只有第1年有长期借款
        if (i === 0) {
            results.longTermLoan[i] = ensureNumber(longTermLoan);
        } else {
            results.longTermLoan[i] = 0;
        }
        
        // 第N年年初借款余额
        if (i === 0) {
            results.beginningLoanBalance[i] = 0; // 第1年年初借款余额为0
        } else {
            results.beginningLoanBalance[i] = results.endingLoanBalance[i-1]; // 第N年年初借款余额=第N-1年期末借款余额
        }
        
        // 第N年本年还本
        if (i === 0) {
            results.annualRepayment[i] = 0; // 第1年不还本金
        } else {
            // 从第2年开始还款，按剩余贷款期限平均还款
            if (i <= loanTerm) { // 确保在贷款期限内还款
                const annualRepayment = longTermLoan / loanTerm;
                // 如果剩余本金小于年还款额，则只还剩余本金
                results.annualRepayment[i] = Math.min(annualRepayment, results.beginningLoanBalance[i]);
            } else {
                results.annualRepayment[i] = 0; // 超过贷款期限后不再还本金
            }
        }
        
        // 第N年本年付息 = 第N年长期借款*借款利率/2+第N年年初借款余额*借款利率
        results.annualInterest[i] = results.longTermLoan[i] * loanRate / 2 + results.beginningLoanBalance[i] * loanRate;
        
        // 第N年期末借款余额 = 年初借款余额 + 长期借款 - 本年还本
        results.endingLoanBalance[i] = results.beginningLoanBalance[i] + results.longTermLoan[i] - results.annualRepayment[i];
        
        // 第N年当期还本付息 = 本年还本 + 本年付息
        results.currentRepaymentAndInterest[i] = results.annualRepayment[i] + results.annualInterest[i];
        
        // 以下默认值都是0
        results.workingCapitalLoanRepayment[i] = 0;
        results.shortTermLoanRepayment[i] = 0;
        results.workingCapitalInterest[i] = 0;
        results.shortTermLoanInterest[i] = 0;
        results.shortTermLoan[i] = 0;
        results.workingCapitalLoan[i] = 0;
        
        // 第N年偿还借款本金 = 本年还本 + 偿还流动资金借款本金 + 偿还短期借款本金
        results.loanPrincipalRepayment[i] = results.annualRepayment[i] + 
                                            results.workingCapitalLoanRepayment[i] + 
                                            results.shortTermLoanRepayment[i];
        
        // 第N年偿还借款利息 = 本年付息 + 流动资金利息 + 短期借款利息
        results.loanInterestRepayment[i] = results.annualInterest[i] + 
                                           results.workingCapitalInterest[i] + 
                                           results.shortTermLoanInterest[i];
        
        // 第N年借款本息合计 = 偿还借款本金 + 偿还借款利息
        results.totalLoanPayment[i] = results.loanPrincipalRepayment[i] + results.loanInterestRepayment[i];
    }

    // 为了更好的可读性，分别显示各个主要计算结果的表格
    if (showLogs) {
        console.log("\n===== 还本付息计算表 =====");
        console.log("注意: 第1年为建设期，第2年开始为运营期");
        console.log(`使用借款利率: ${(loanRate * 100).toFixed(2)}%`);
        
        // 显示借款状况
        const loanStatusResults = {};
        for (let i = 0; i < years; i++) {
            loanStatusResults[`第${i+1}年`] = {
                "长期借款": results.longTermLoan[i],
                "年初借款余额": results.beginningLoanBalance[i],
                "本年还本": results.annualRepayment[i],
                "本年付息": results.annualInterest[i],
                "期末借款余额": results.endingLoanBalance[i],
                "当期还本付息": results.currentRepaymentAndInterest[i]
            };
        }
        console.log("1. 长期借款情况");
        console.table(loanStatusResults);
        
        // 显示各类借款明细
        const loanDetailsResults = {};
        for (let i = 0; i < years; i++) {
            loanDetailsResults[`第${i+1}年`] = {
                "偿还借款本金": results.loanPrincipalRepayment[i],
                "偿还借款利息": results.loanInterestRepayment[i],
                "借款本息合计": results.totalLoanPayment[i],
                "流动资金借款": results.workingCapitalLoan[i],
                "短期借款": results.shortTermLoan[i]
            };
        }
        console.log("\n2. 借款明细");
        console.table(loanDetailsResults);
        
        // 显示其他借款相关费用
        const otherLoanExpensesResults = {};
        for (let i = 0; i < years; i++) {
            otherLoanExpensesResults[`第${i+1}年`] = {
                "偿还流动资金借款本金": results.workingCapitalLoanRepayment[i],
                "偿还短期借款本金": results.shortTermLoanRepayment[i],
                "流动资金利息": results.workingCapitalInterest[i],
                "短期借款利息": results.shortTermLoanInterest[i]
            };
        }
        console.log("\n3. 其他借款相关费用");
        console.table(otherLoanExpensesResults);
    }

    return results;
}

module.exports = { calculateLoanRepaymentSchedule }; 