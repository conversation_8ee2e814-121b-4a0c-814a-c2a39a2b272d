// 财务测算模型测试文件
const { cloneDeep }  = require('lodash');
const { runFinancialModel } = require('./index');
const getDevicePrice = (inputs) => {
  const {
    pvEPC,
    windEPC,
    batEPC,
    alkEPC,
    alkStoreEPC, // 元/m3
  
    pvCapacity, // MW
    windCapacity,
    batCapacity,
    alkCapacity,
    alkStoreCapacity, // m³
    
    // pv ratio
    pvEquipmentRatio,
    pvInstallRatio,
    pvBuildingRatio,
    pvOthersRatio,

    // wind ratio
    windEquipmentRatio,
    windInstallRatio,
    windBuildingRatio,
    windOthersRatio,

    // bat ratio
    batEquipmentRatio,
    batInstallRatio,
    batBuildingRatio,
    batOthersRatio,


    // alk ratio
    alkEquipmentRatio,
    alkInstallRatio,
    alkBuildingRatio,
    alkOthersRatio,

    // alkStore ratio
    alkStoreEquipmentRatio,
    alkStoreInstallRatio,
    alkStoreBuildingRatio,
    alkStoreOthersRatio,

    // 土地租赁单价
    pvLandUnitPrice, // 元/W 
    windLandUnitPrice, // 元/W

    isFromBe
  } = inputs;

  const pvPrice = pvCapacity * pvEPC * 100; // 万元
  const windPrice = windCapacity * windEPC * 100; // 万元
  const batPrice = batCapacity * batEPC * 100; // 万元
  const alkPrice = alkCapacity * alkEPC * 100; // 万元
  const alkStorePrice = alkStoreCapacity * alkStoreEPC / 10000; // 万元

  const baseParams = {
    installedCapacity: pvCapacity + windCapacity,

    windTurbineLandRentalFeeYear2: windCapacity * windLandUnitPrice * 100, // 风机第2年土地租赁费(单位：万元)
    photovoltaicLandRentalFeeYear2: pvCapacity * pvLandUnitPrice * 100, // 光伏第2年土地租赁费(单位：万元)
    windTurbineLandTaxAnnual: windCapacity * windLandUnitPrice * 100 * 0.03, // 风机土地税费 
    photovoltaicLandTaxAnnual: pvCapacity * pvLandUnitPrice * 100 * 0.03, // 光伏土地税费
  }
  return isFromBe ? {
    ...baseParams,
    windTurbineEquipment: windPrice * windEquipmentRatio, // 风机设备(单位：万元)
    windTurbineInstallation: windPrice * windInstallRatio, // 风机安装(单位：万元)
    windTurbineBuilding: windPrice * windBuildingRatio, // 风机建筑(单位：万元)
    windTurbineOthers: windPrice * windOthersRatio, // 风机其他(单位：万元)


    photovoltaicEquipment: pvPrice * pvEquipmentRatio, // 光伏设备(单位：万元)
    photovoltaicInstallation: pvPrice * pvInstallRatio, // 光伏安装(单位：万元)
    photovoltaicBuilding: pvPrice * pvBuildingRatio, // 光伏建筑(单位：万元)
    photovoltaicOthers: pvPrice * pvOthersRatio, // 光伏其他(单位：万元)



    energyStorageEquipment: batPrice * batEquipmentRatio, // 储能设备(单位：万元)
    energyStorageInstallation: batPrice * batInstallRatio, // 储能安装(单位：万元)
    energyStorageBuilding: batPrice * batBuildingRatio, // 储能建筑(单位：万元)
    energyStorageOthers: batPrice * batOthersRatio, // 储能其他(单位：万元)
    
    electrolyzerEquipment: alkPrice * alkEquipmentRatio, // 电解槽设备(单位：万元)
    electrolyzerInstallation: alkPrice * alkInstallRatio, // 电解槽安装(单位：万元)
    electrolyzerBuilding: alkPrice * alkBuildingRatio, // 电解槽建筑(单位：万元)
    electrolyzerOthers: alkPrice * alkOthersRatio, // 电解槽其他(单位：万元)


    hydrogenStorageEquipment: alkStorePrice * alkStoreEquipmentRatio, // 储罐设备(单位：万元)
    hydrogenStorageInstallation: alkStorePrice * alkStoreInstallRatio, // 储罐安装(单位：万元)
    hydrogenStorageBuilding: alkStorePrice * alkStoreBuildingRatio, // 储罐建筑(单位：万元)
    hydrogenStorageOthers: alkStorePrice * alkStoreOthersRatio, // 储罐其他(单位：万元)
  } : baseParams
};


function getAnalysis(analysisParams, currentParams) {
  const resultAll = [];
  analysisParams.forEach(item => {
    const { key, value, type } = item;
    const itemResult = [];
    value.forEach(i => {
      let r;
      if (type === 0) {
        r = runFinancialModel({ ...currentParams, [key]: currentParams[key]  * (1 + i) })
      } else if (type === 1) {
        r = runFinancialModel({ ...currentParams, [key]: currentParams[key]  + i })
      } else if (type === -1) {
        if (key === 'investCost') {
          const baseParam = {
            ...currentParams,
            pvEPC: currentParams.pvEPC * (1 + i),
            windEPC: currentParams.windEPC * (1 + i),
            batEPC: currentParams.batEPC * (1 + i),
            alkEPC: currentParams.alkEPC * (1 + i),
            alkStoreEPC: currentParams.alkStoreEPC * (1 + i),
          }
          // console.log('baseParam', baseParam)
          r = runFinancialModel(baseParam)
        }
      }
      const { projectInvestmentFIRR_afterTax, equityCapitalFIRR_afterTax } = r.financialIndicatorsSummary;
      itemResult.push({ projectInvestmentFIRR_afterTax, equityCapitalFIRR_afterTax });
    });
    resultAll.push({
      ...item,
      result: itemResult
    });
  });
  return resultAll;
};

// 测试用例：显示表格数据
function getFinanceData(inputs, showLogs) {
    console.time('计算时间');
    const inputParams = typeof inputs === 'string' ? JSON.parse(inputs) : inputs;
    const currentParams =  {
      ...inputParams,
      ...getDevicePrice(inputParams)
    }
    const resultTables = runFinancialModel(
      currentParams,
      showLogs
    );
    const resultAnalysis = getAnalysis([
      { key: 'investCost',
        value: [0.1, 0.05, -0.05, -0.1], // 投资成本
        type: -1
      },
      { key: 'gridElectricityPriceWithTax', value: [0.1, 0.05, -0.05, -0.1],   type: 0 },
      { key: 'gridElectricityPriceWithTax', value: [0.02, 0.01, -0.01, -0.02], type: 1 },
      { key: 'baseLoanRate',                value: [0.1, 0.05, -0.05, -0.1],   type: 1 }, 
      { key: 'financingRatioBase',          value: [0.1, 0.05, -0.05, -0.1],   type: 1 },  //  贷款比例
      { key: 'hydrogenPriceWithTax',        value: [0.1, 0.05, -0.05, -0.1],   type: 1 }, 
    ], currentParams)
    // console.log(JSON.stringify(resultAnalysis, null, 2))

    console.timeEnd('计算时间');
    return { resultTables,  resultAnalysis};
};

// const results = getFinanceData(testInputs);
// console.log(results);

module.exports = { getFinanceData };
