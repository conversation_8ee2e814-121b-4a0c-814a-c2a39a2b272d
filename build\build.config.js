const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 构建配置
const BUILD_CONFIG = {
  // 输出目录
  outputDir: '../dist',
  
  // 清理选项
  clean: true,
  
  // 压缩选项
  minify: {
    enabled: true,
    options: {
      compress: {
        drop_console: true,
        drop_debugger: true,
        pure_funcs: ['console.log', 'console.time', 'console.timeEnd']
      },
      mangle: {
        reserved: [
          'runFinancialModel',
          'getFinanceData',
          'calculateKeyIndicators',
          'calculateFixedAssetsInvestmentEstimation'
        ]
      }
    }
  },
  
  // 目标格式
  targets: [
    { format: 'cjs', filename: 'finance-model.cjs.js' },
    { format: 'esm', filename: 'finance-model.esm.js' },
    { format: 'umd', filename: 'finance-model.umd.js' }
  ]
};

// 清理输出目录
function cleanDistDir() {
  if (BUILD_CONFIG.clean && fs.existsSync(BUILD_CONFIG.outputDir)) {
    fs.rmSync(BUILD_CONFIG.outputDir, { recursive: true, force: true });
    console.log('🧹 清理输出目录完成');
  }
}

// 创建输出目录
function ensureOutputDir() {
  if (!fs.existsSync(BUILD_CONFIG.outputDir)) {
    fs.mkdirSync(BUILD_CONFIG.outputDir, { recursive: true });
  }
}

// 执行构建
function build() {
  console.log('🚀 开始构建财务模型...');
  
  try {
    cleanDistDir();
    ensureOutputDir();
    
    // 执行 Rollup 构建
    console.log('📦 执行 Rollup 构建...');
    execSync('npx rollup -c rollup.config.js', { stdio: 'inherit' });
    
    // 执行 Webpack 构建
    console.log('📦 执行 Webpack 构建...');
    execSync('npx webpack --mode production', { stdio: 'inherit' });
    
    // 生成构建报告
    generateBuildReport();
    
    console.log('✅ 构建完成！');
    console.log(`📁 输出目录: ${path.resolve(BUILD_CONFIG.outputDir)}`);
    
  } catch (error) {
    console.error('❌ 构建失败:', error.message);
    process.exit(1);
  }
}

// 生成构建报告
function generateBuildReport() {
  const report = {
    buildTime: new Date().toISOString(),
    files: [],
    totalSize: 0
  };
  
  // 扫描输出文件
  function scanDir(dir, baseDir = '') {
    const items = fs.readdirSync(dir);
    items.forEach(item => {
      const fullPath = path.join(dir, item);
      const relativePath = path.join(baseDir, item);
      const stats = fs.statSync(fullPath);
      
      if (stats.isDirectory()) {
        scanDir(fullPath, relativePath);
      } else {
        const size = stats.size;
        report.files.push({
          path: relativePath,
          size: size,
          sizeFormatted: formatFileSize(size)
        });
        report.totalSize += size;
      }
    });
  }
  
  if (fs.existsSync(BUILD_CONFIG.outputDir)) {
    scanDir(BUILD_CONFIG.outputDir);
  }
  
  report.totalSizeFormatted = formatFileSize(report.totalSize);
  
  // 写入报告文件
  fs.writeFileSync(
    path.join(BUILD_CONFIG.outputDir, 'build-report.json'),
    JSON.stringify(report, null, 2)
  );
  
  console.log(`📊 构建报告: 生成了 ${report.files.length} 个文件，总大小: ${report.totalSizeFormatted}`);
}

// 格式化文件大小
function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 如果直接运行此脚本
if (require.main === module) {
  build();
}

module.exports = { build, BUILD_CONFIG }; 