/**
 * 计算财务计划现金流量表
 * 注意：所有数据均应严格按照经评测算模型定义直接引用对应表格数据
 * @param {Object} params - 输入参数
 * @param {boolean} showLogs - 是否显示表格数据（可选，默认为false）
 * @returns {Object} - 财务计划现金流量表结果
 */
function calculateFinancialPlanCashFlow(params, showLogs = false) {
    const {
        operatingYears, // 项目运营年限
        operatingIncomeWithTax, // 第N年含税营业收入 - 来自8项目投资现金流量表
        salesTax, // 第N年销项税 - 来自6利润和利润分配表
        taxableSubsidyIncome, // 第N年补贴收入应税 - 来自6利润和利润分配表
        operatingCost, // 第N年经营成本 - 来自8项目投资现金流量表
        inputTax, // 第N年进项税 - 来自5总成本费用表
        salesTaxAndSurcharge, // 第N年销售税金及附加 - 来自6利润和利润分配表
        vatPayable, // 第N年应缴税金 - 来自6利润和利润分配表
        incomeTax, // 第N年所得税 - 来自6利润和利润分配表
        fixedAssetsResidualValue, // 第N年回收固定资产余值 - 来自8项目投资现金流量表
        workingCapitalRecovery, // 第N年回收流动资金 - 来自8项目投资现金流量表
        fixedAssetsStaticInvestment, // 固定资产静态投资 - 来自3固定资产投资估算表
        workingCapital, // 流动资金 - 来自3固定资产投资估算表或4投资计划与资金筹措表
        fundRaising, // 资金筹措 - 来自4投资计划与资金筹措表
        equityCapital, // 资本金 - 来自2融资计划表
        bankLoan, // 银行借款 - 来自2融资计划表
        annualRepayment, // 第N年本年还本 - 来自7还本付息计算表
        annualInterest, // 第N年本年付息 - 来自7还本付息计算表
        payableProfit // 第N年应付利润 - 来自6利润和利润分配表
    } = params;

    // 创建结果对象，包括所有年份的数据
    const years = operatingYears + 1; // +1 是因为包括第1年(建设期)

    // 确保数组参数有效
    const ensureArray = (arr, length) => arr && arr.length ? [...arr] : new Array(length).fill(0);
    
    const results = {
        // 经营活动现金流量相关
        operatingIncomeWithTax: ensureArray(operatingIncomeWithTax, years), // 第N年含税营业收入(营业收入) - 8项目投资现金流量表
        salesTax: ensureArray(salesTax, years), // 第N年销项税 - 6利润和利润分配表
        taxableSubsidyIncome: ensureArray(taxableSubsidyIncome, years), // 第N年补贴收入应税 - 6利润和利润分配表
        operatingActivityOtherInflow: new Array(years).fill(0), // 第N年经营活动其他流入
        operatingActivityCashInflow: new Array(years).fill(0), // 第N年经营活动现金流入
        operatingCost: ensureArray(operatingCost, years), // 第N年经营成本 - 8项目投资现金流量表
        inputTax: ensureArray(inputTax, years), // 第N年增值税进项税额 - 5总成本费用表
        salesTaxAndSurcharge: ensureArray(salesTaxAndSurcharge, years), // 第N年销售税金及附加 - 6利润和利润分配表
        vatPayable: ensureArray(vatPayable, years), // 第N年应缴税金(增值税) - 6利润和利润分配表
        incomeTax: ensureArray(incomeTax, years), // 第N年所得税 - 6利润和利润分配表
        operatingActivityOtherOutflow: new Array(years).fill(0), // 第N年经营活动其他流出
        operatingActivityCashOutflow: new Array(years).fill(0), // 第N年经营活动现金流出
        operatingActivityNetCashFlow: new Array(years).fill(0), // 第N年经营活动净现金流量
        
        // 投资活动现金流量相关
        investmentActivityCashInflow: new Array(years).fill(0), // 第N年投资活动现金流入
        fixedAssetsResidualValue: ensureArray(fixedAssetsResidualValue, years), // 第N年回收固定资产余值 - 8项目投资现金流量表
        workingCapitalRecovery: ensureArray(workingCapitalRecovery, years), // 第N年回收流动资金 - 8项目投资现金流量表
        investmentActivityBuildingInvestment: new Array(years).fill(0), // 第N年投资活动建设投资
        investmentActivityOperationInvestment: new Array(years).fill(0), // 第N年投资活动维持运营投资
        investmentActivityWorkingCapital: new Array(years).fill(0), // 第N年投资活动流动资金
        investmentActivityOtherOutflow: new Array(years).fill(0), // 第N年投资活动其他流出
        investmentActivityCashOutflow: new Array(years).fill(0), // 第N年投资活动现金流出
        investmentActivityNetCashFlow: new Array(years).fill(0), // 第N年投资活动净现金流量
        
        // 筹资活动现金流量相关
        financingActivityCashInflow: new Array(years).fill(0), // 第N年筹资活动现金流入
        equityCapitalInvestment: new Array(years).fill(0), // 第N年项目资本金投入
        buildingInvestmentLoan: new Array(years).fill(0), // 第N年建设投资借款
        workingCapitalLoan: new Array(years).fill(0), // 第N年流动资金借款
        bond: new Array(years).fill(0), // 第N年债券
        shortTermLoan: new Array(years).fill(0), // 第N年短期借款
        financingActivityOtherInflow: new Array(years).fill(0), // 第N年筹资活动其他流入
        interestExpense: new Array(years).fill(0), // 第N年各种利息支出
        debtPrincipalRepayment: new Array(years).fill(0), // 第N年偿还债务本金
        payableProfit: ensureArray(payableProfit, years), // 第N年应付利润 - 6利润和利润分配表
        financingActivityOtherOutflow: new Array(years).fill(0), // 第N年筹资活动其他流出
        financingActivityCashOutflow: new Array(years).fill(0), // 第N年筹资活动现金流出
        financingActivityNetCashFlow: new Array(years).fill(0), // 第N年筹资活动净现金流量
        
        // 汇总数据
        netCashFlow: new Array(years).fill(0), // 第N年财务计划净现金流量
        accumulatedSurplusFunds: new Array(years).fill(0) // 第N年财务计划累计盈余资金
    };

    // 计算各年数据
    for (let i = 0; i < years; i++) {
        // 1. 经营活动现金流量
        // 第N年经营活动其他流入 - 根据经评测算模型定义
        // 按照经评测算模型：
        // - 第N(1<=N<=项目运营年限)年财务计划_经营活动_其他流入 = 0
        // - 第N(N=项目运营年限+1)年财务计划_经营活动_其他流入 = 第N年回收流动资金
        if (i === operatingYears) {
            // 最后一年（N=项目运营年限+1）的其他流入
            results.operatingActivityOtherInflow[i] = results.workingCapitalRecovery[i]; // 根据经评测算模型，为回收流动资金
        } else {
            results.operatingActivityOtherInflow[i] = 0;
        }
        
        // 第N年经营活动现金流入 = 第N年含税营业收入 + 第N年增值税销项税额 + 第N年补贴收入不含税 + 第N年经营活动其他流入
        // 注：根据经评测算模型，增值税销项税额只在第1年有值，其他年份为0
        results.operatingActivityCashInflow[i] = 
            results.operatingIncomeWithTax[i] + 
            (i === 0 ? results.salesTax[i] : 0) + 
            results.taxableSubsidyIncome[i] + 
            results.operatingActivityOtherInflow[i];
        
        // 第N年经营活动现金流出 = 第N年经营成本 + 第N年增值税进项税额 + 第N年销售税金及附加 + 第N年增值税 + 第N年所得税 + 第N年经营活动其他流出
        results.operatingActivityCashOutflow[i] = 
            results.operatingCost[i] + 
            results.inputTax[i] + 
            results.salesTaxAndSurcharge[i] + 
            results.vatPayable[i] + 
            results.incomeTax[i] + 
            results.operatingActivityOtherOutflow[i];
        
        // 第N年经营活动净现金流量 = 第N年经营活动现金流入 - 第N年经营活动现金流出
        results.operatingActivityNetCashFlow[i] = 
            results.operatingActivityCashInflow[i] - results.operatingActivityCashOutflow[i];
        
        // 2. 投资活动现金流量
        // 第N年投资活动现金流入 - 根据经评测算模型定义
        if (i <= operatingYears - 1) {
            results.investmentActivityCashInflow[i] = 0;
        } else if (i === operatingYears) {
            // 最后一年（N=项目运营年限+1）的投资活动现金流入
            // 按照经评测算：回收固定资产余值和回收流动资金
            results.investmentActivityCashInflow[i] = results.fixedAssetsResidualValue[i] + results.workingCapitalRecovery[i];
        }
        
        // 第N年投资活动建设投资 - 根据经评测算模型定义，第1年为固定资产静态投资，其他年份为0
        if (i === 0) {
            results.investmentActivityBuildingInvestment[i] = fixedAssetsStaticInvestment || 0;
        } else {
            results.investmentActivityBuildingInvestment[i] = 0;
        }
        
        // 第N年投资活动流动资金 - 根据经评测算模型定义，第1年为流动资金，其他年份为0
        if (i === 0) {
            results.investmentActivityWorkingCapital[i] = workingCapital || 0;
        } else {
            results.investmentActivityWorkingCapital[i] = 0;
        }
        
        // 第N年投资活动现金流出 = 第N年投资活动建设投资 + 第N年投资活动维持运营投资 + 第N年投资活动流动资金 + 第N年投资活动其他流出
        // 注：根据经评测算模型定义，仅第1年有值，其他年份为0
        if (i === 0) {
            results.investmentActivityCashOutflow[i] = 
                results.investmentActivityBuildingInvestment[i] + 
                results.investmentActivityOperationInvestment[i] + 
                results.investmentActivityWorkingCapital[i] + 
                results.investmentActivityOtherOutflow[i];
        } else {
            results.investmentActivityCashOutflow[i] = 0;
        }
        
        // 第N年投资活动净现金流量 = 第N年投资活动现金流入 - 第N年投资活动现金流出
        results.investmentActivityNetCashFlow[i] = 
            results.investmentActivityCashInflow[i] - results.investmentActivityCashOutflow[i];
        
        // 3. 筹资活动现金流量
        // 第N年项目资本金投入 - 根据经评测算模型定义，第1年为资本金，其他年份为0
        if (i === 0) {
            results.equityCapitalInvestment[i] = equityCapital || 0;
        } else {
            results.equityCapitalInvestment[i] = 0;
        }
        
        // 第N年建设投资借款 - 根据经评测算模型定义，第1年为银行借款，其他年份为0
        if (i === 0) {
            results.buildingInvestmentLoan[i] = bankLoan || 0;
        } else {
            results.buildingInvestmentLoan[i] = 0;
        }
        
        // 第N年筹资活动现金流入 = 资金筹措(第1年) 或 0(其他年份)
        // 注：根据经评测算模型定义，第1年为资金筹措，其他年份为0
        if (i === 0) {
            results.financingActivityCashInflow[i] = fundRaising || 0; // 确保不为undefined
        } else {
            results.financingActivityCashInflow[i] = 0;
        }
        
        // 第N年各种利息支出 = 第N年本年付息 - 来自7还本付息计算表
        // 确保数组存在且有效
        const interestArray = ensureArray(annualInterest, years);
        results.interestExpense[i] = interestArray[i];
        
        // 第N年偿还债务本金 = 第N年本年还本 - 来自7还本付息计算表
        // 确保数组存在且有效
        const repaymentArray = ensureArray(annualRepayment, years);
        results.debtPrincipalRepayment[i] = repaymentArray[i];
        
        // 第N年筹资活动现金流出 = 第N年各种利息支出 + 第N年偿还债务本金 + 第N年应付利润 + 第N年筹资活动其他流出
        results.financingActivityCashOutflow[i] = 
            results.interestExpense[i] + 
            results.debtPrincipalRepayment[i] + 
            results.payableProfit[i] + 
            results.financingActivityOtherOutflow[i];
        
        // 第N年筹资活动净现金流量 = 第N年筹资活动现金流入 - 第N年筹资活动现金流出
        results.financingActivityNetCashFlow[i] = 
            results.financingActivityCashInflow[i] - results.financingActivityCashOutflow[i];
        
        // 4. 汇总数据
        // 第N年财务计划净现金流量 = 第N年经营活动净现金流量 + 第N年投资活动净现金流量 + 第N年筹资活动净现金流量
        results.netCashFlow[i] = 
            results.operatingActivityNetCashFlow[i] + 
            results.investmentActivityNetCashFlow[i] + 
            results.financingActivityNetCashFlow[i];
        
        // 第N年财务计划累计盈余资金
        if (i === 0) {
            results.accumulatedSurplusFunds[i] = results.netCashFlow[i];
        } else {
            results.accumulatedSurplusFunds[i] = results.accumulatedSurplusFunds[i-1] + results.netCashFlow[i];
        }
    }

    // 打印结果，按类别分组以提高可读性
    if (showLogs) {
        console.log("\n===== 财务计划现金流量表 =====");
        
        // 1. 经营活动现金流量 - 打印所有第一级子项
        const operatingActivityTable = {};
        for (let i = 0; i < years; i++) {
            operatingActivityTable[`第${i}年`] = {
                "营业收入": results.operatingIncomeWithTax[i],
                "增值税销项税额": i === 0 ? results.salesTax[i] : 0,
                "补贴收入不含税": results.taxableSubsidyIncome[i],
                "其他流入": results.operatingActivityOtherInflow[i],
                "经营活动现金流入": results.operatingActivityCashInflow[i],
                "经营成本": results.operatingCost[i],
                "增值税进项税额": results.inputTax[i],
                "税金及附加": results.salesTaxAndSurcharge[i],
                "增值税": results.vatPayable[i],
                "所得税": results.incomeTax[i],
                "其他流出": results.operatingActivityOtherOutflow[i],
                "经营活动现金流出": results.operatingActivityCashOutflow[i],
                "经营活动净现金流量": results.operatingActivityNetCashFlow[i]
            };
        }
        console.log("1. 经营活动现金流量");
        console.table(operatingActivityTable);
        
        // 2. 投资活动现金流量 - 打印所有第一级子项
        const investmentActivityTable = {};
        for (let i = 0; i < years; i++) {
            investmentActivityTable[`第${i}年`] = {
                "回收固定资产余值": results.fixedAssetsResidualValue[i],
                "回收流动资金": results.workingCapitalRecovery[i],
                "投资活动现金流入": results.investmentActivityCashInflow[i],
                "建设投资": results.investmentActivityBuildingInvestment[i],
                "维持运营投资": results.investmentActivityOperationInvestment[i],
                "流动资金": results.investmentActivityWorkingCapital[i],
                "其他流出": results.investmentActivityOtherOutflow[i],
                "投资活动现金流出": results.investmentActivityCashOutflow[i],
                "投资活动净现金流量": results.investmentActivityNetCashFlow[i]
            };
        }
        console.log("\n2. 投资活动现金流量");
        console.table(investmentActivityTable);
        
        // 3. 筹资活动现金流量 - 打印所有第一级子项
        const financingActivityTable = {};
        for (let i = 0; i < years; i++) {
            financingActivityTable[`第${i}年`] = {
                "项目资本金投入": results.equityCapitalInvestment[i],
                "建设投资借款": results.buildingInvestmentLoan[i],
                "流动资金借款": results.workingCapitalLoan[i],
                "债券": results.bond[i],
                "短期借款": results.shortTermLoan[i],
                "其他流入": results.financingActivityOtherInflow[i],
                "筹资活动现金流入": results.financingActivityCashInflow[i],
                "各种利息支出": results.interestExpense[i],
                "偿还债务本金": results.debtPrincipalRepayment[i],
                "应付利润": results.payableProfit[i],
                "其他流出": results.financingActivityOtherOutflow[i],
                "筹资活动现金流出": results.financingActivityCashOutflow[i],
                "筹资活动净现金流量": results.financingActivityNetCashFlow[i]
            };
        }
        console.log("\n3. 筹资活动现金流量");
        console.table(financingActivityTable);
        
        // 4. 现金流量汇总 - 打印所有第一级子项
        const cashFlowSummaryTable = {};
        for (let i = 0; i < years; i++) {
            cashFlowSummaryTable[`第${i}年`] = {
                "经营活动净现金流量": results.operatingActivityNetCashFlow[i],
                "投资活动净现金流量": results.investmentActivityNetCashFlow[i],
                "筹资活动净现金流量": results.financingActivityNetCashFlow[i],
                "财务计划净现金流量": results.netCashFlow[i],
                "财务计划累计盈余资金": results.accumulatedSurplusFunds[i]
            };
        }
        console.log("\n4. 现金流量汇总");
        console.table(cashFlowSummaryTable);
    }

    return results;
}

module.exports = { calculateFinancialPlanCashFlow }; 