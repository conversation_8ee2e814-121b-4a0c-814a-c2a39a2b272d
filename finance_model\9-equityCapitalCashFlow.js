/**
 * 计算资本金财务现金流量表
 * @param {Object} params - 输入参数
 * @param {boolean} showLogs - 是否显示表格数据（可选，默认为false）
 * @returns {Object} - 资本金财务现金流量表结果
 */
function calculateEquityCapitalCashFlow(params, showLogs = false) {
    const {
        operatingYears, // 项目运营年限
        equityCapital, // 资本金
        netProfit, // 第N年净利润
        depreciation, // 第N年折旧费
        amortization, // 第N年摊销费
        longTermLoan, // 第N年长期借款
        loanPrincipalRepayment, // 第N年偿还借款本金
        annualInterest, // 第N年本年付息
        shortTermLoanInterest, // 第N年短期借款利息
        workingCapitalInterest, // 第N年流动资金利息
        operatingIncomeWithTax, // 第N年含税营业收入
        subsidyIncome, // 第N年补贴收入
        operatingCost, // 第N年经营成本
        inputTaxPayment, // 第N年增值税进项税缴纳
        salesTaxAndSurcharge, // 第N年销售税金及附加
        incomeTax, // 第N年所得税
        vatPayable, // 第N年维持运营投资(缴纳增值税)
        fixedAssetsNoTax, // 固定资产不含税
        salvageRate, // 残值率
        workingCapital, // 流动资金
        payableProfit, // 第N年应付利润
        discountRate // 折现率
    } = params;

    // 创建结果对象，包括所有年份的数据
    const years = operatingYears + 1; // +1 是因为包括第1年(建设期)
    const results = {
        // 直接引入其他表的数据
        operatingIncomeWithTax: operatingIncomeWithTax ? [...operatingIncomeWithTax] : new Array(years).fill(0), // 第N年含税营业收入
        subsidyIncome: subsidyIncome ? [...subsidyIncome] : new Array(years).fill(0), // 第N年补贴收入
        projectCapitalInvestment: new Array(years).fill(0), // 第N年项目资本金
        loanPrincipalRepayment: loanPrincipalRepayment ? [...loanPrincipalRepayment] : new Array(years).fill(0), // 第N年偿还借款本金
        annualInterest: annualInterest ? [...annualInterest] : new Array(years).fill(0), // 第N年本年付息
        shortTermLoanInterest: shortTermLoanInterest ? [...shortTermLoanInterest] : new Array(years).fill(0), // 第N年短期借款利息
        workingCapitalInterest: workingCapitalInterest ? [...workingCapitalInterest] : new Array(years).fill(0), // 第N年流动资金利息
        operatingCost: operatingCost ? [...operatingCost] : new Array(years).fill(0), // 第N年经营成本
        inputTaxPayment: inputTaxPayment ? [...inputTaxPayment] : new Array(years).fill(0), // 第N年增值税进项税缴纳
        salesTaxAndSurcharge: salesTaxAndSurcharge ? [...salesTaxAndSurcharge] : new Array(years).fill(0), // 第N年销售税金及附加
        incomeTax: incomeTax ? [...incomeTax] : new Array(years).fill(0), // 第N年所得税
        vatPayable: vatPayable ? [...vatPayable] : new Array(years).fill(0), // 第N年维持运营投资
        fixedAssetsResidualValue: new Array(years).fill(0), // 第N年回收固定资产余值
        workingCapitalRecovery: new Array(years).fill(0), // 第N年回收流动资金
        payableProfit: payableProfit ? [...payableProfit] : new Array(years).fill(0), // 第N年应付利润
        loanInterestRepayment: new Array(years).fill(0), // 第N年偿还借款利息
        
        // 需要计算的第一级子数据
        capitalCashInflow: new Array(years).fill(0), // 第N年资本金现金流入
        capitalCashOutflow: new Array(years).fill(0), // 第N年资本金现金流出
        capitalNetCashFlow: new Array(years).fill(0), // 第N年资本金税后净现金流量
        accumulatedNetCashFlow: new Array(years).fill(0), // 第N年资本金累计净现金流量
        capitalPretaxNetCashFlow: new Array(years).fill(0), // 第N年资本金税前净现金流量
        accumulatedPretaxNetCashFlow: new Array(years).fill(0), // 第N年资本金税前累计净现金流量
        discountFactor: new Array(years).fill(0), // 第N年折现系数
        presentValue: new Array(years).fill(0), // 第N年现值
        accumulatedPresentValue: new Array(years).fill(0) // 第N年累计现值
    };

    // 计算第N年项目资本金 - 第1年为资本金，其他年份为0
    results.projectCapitalInvestment[0] = equityCapital;

    // 计算第N年回收固定资产余值和回收流动资金
    if (years > operatingYears) {
        results.fixedAssetsResidualValue[operatingYears] = fixedAssetsNoTax * salvageRate;
        results.workingCapitalRecovery[operatingYears] = workingCapital;
    }

    // 计算第一级子数据
    for (let i = 0; i < years; i++) {
        // 计算第N年偿还借款利息 = 第N年本年付息 + 第N年流动资金利息 + 第N年短期借款利息
        results.loanInterestRepayment[i] = 
            results.annualInterest[i] + 
            results.workingCapitalInterest[i] + 
            results.shortTermLoanInterest[i];
            
        // 第N年资本金现金流入 = 第N年含税营业收入 + 第N年补贴收入 + 第N年回收固定资产余值 + 第N年回收流动资金
        results.capitalCashInflow[i] = 
            results.operatingIncomeWithTax[i] + 
            results.subsidyIncome[i] + 
            results.fixedAssetsResidualValue[i] + 
            results.workingCapitalRecovery[i];
        
        // 第N年资本金现金流出 = 第N年项目资本金 + 第N年偿还借款本金 + 第N年偿还借款利息 + 第N年经营成本 + 
        // 第N年增值税进项税缴纳 + 第N年销售税金及附加 + 第N年所得税 + 第N年维持运营投资
        results.capitalCashOutflow[i] = 
            results.projectCapitalInvestment[i] + 
            results.loanPrincipalRepayment[i] + 
            results.loanInterestRepayment[i] + 
            results.operatingCost[i] + 
            results.inputTaxPayment[i] + 
            results.salesTaxAndSurcharge[i] + 
            results.incomeTax[i] + 
            results.vatPayable[i];
        
        // 第N年资本金税后净现金流量 = 第N年资本金现金流入 - 第N年资本金现金流出 - 第N年应付利润
        results.capitalNetCashFlow[i] = results.capitalCashInflow[i] - results.capitalCashOutflow[i] - results.payableProfit[i];
        
        // 第N年资本金税前净现金流量 = 第N年资本金税后净现金流量 + 第N年所得税
        results.capitalPretaxNetCashFlow[i] = results.capitalNetCashFlow[i] + results.incomeTax[i];
        
        // 第N年折现系数 = 1 / (1 + 折现率)^N
        results.discountFactor[i] = 1 / Math.pow(1 + discountRate, i);
        
        // 第N年现值 = 第N年资本金净现金流量 * 第N年折现系数
        results.presentValue[i] = results.capitalNetCashFlow[i] * results.discountFactor[i];
        
        // 计算累计数值
        if (i === 0) {
            // 第1年累计值
            results.accumulatedNetCashFlow[i] = results.capitalNetCashFlow[i];
            results.accumulatedPretaxNetCashFlow[i] = results.capitalPretaxNetCashFlow[i];
            results.accumulatedPresentValue[i] = results.presentValue[i];
        } else {
            // 第N(N>=2)年累计值
            results.accumulatedNetCashFlow[i] = results.capitalNetCashFlow[i] + results.accumulatedNetCashFlow[i-1];
            results.accumulatedPretaxNetCashFlow[i] = results.capitalPretaxNetCashFlow[i] + results.accumulatedPretaxNetCashFlow[i-1];
            results.accumulatedPresentValue[i] = results.presentValue[i] + results.accumulatedPresentValue[i-1];
        }
    }

    // 打印结果，按分类显示
    if (showLogs) {
        console.log("\n===== 资本金财务现金流量表 =====");
        
        // 1. 资本金现金流入相关数据
        const cashInflowTable = {};
        for (let i = 0; i < years; i++) {
            cashInflowTable[`第${i}年`] = {
                "含税营业收入": results.operatingIncomeWithTax[i],
                "补贴收入": results.subsidyIncome[i],
                "回收固定资产余值": results.fixedAssetsResidualValue[i],
                "回收流动资金": results.workingCapitalRecovery[i],
                "资本金现金流入": results.capitalCashInflow[i]
            };
        }
        console.log("\n1. 资本金现金流入");
        console.table(cashInflowTable);
        
        // 2. 资本金现金流出相关数据
        const cashOutflowTable = {};
        for (let i = 0; i < years; i++) {
            cashOutflowTable[`第${i}年`] = {
                "项目资本金": results.projectCapitalInvestment[i],
                "偿还借款本金": results.loanPrincipalRepayment[i],
                "偿还借款利息": results.loanInterestRepayment[i],
                "经营成本": results.operatingCost[i],
                "增值税进项税缴纳": results.inputTaxPayment[i],
                "销售税金及附加": results.salesTaxAndSurcharge[i],
                "所得税": results.incomeTax[i],
                "维持运营投资": results.vatPayable[i],
                "资本金现金流出": results.capitalCashOutflow[i]
            };
        }
        console.log("\n2. 资本金现金流出");
        console.table(cashOutflowTable);
        
        // 3. 净现金流量及其他相关数据
        const netCashFlowTable = {};
        for (let i = 0; i < years; i++) {
            netCashFlowTable[`第${i}年`] = {
                "应付利润": results.payableProfit[i],
                "资本金税后净现金流量": results.capitalNetCashFlow[i],
                "资本金累计净现金流量": results.accumulatedNetCashFlow[i],
                "资本金税前净现金流量": results.capitalPretaxNetCashFlow[i],
                "资本金税前累计净现金流量": results.accumulatedPretaxNetCashFlow[i]
            };
        }
        console.log("\n3. 资本金净现金流量");
        console.table(netCashFlowTable);
    }

    return results;
}

module.exports = { calculateEquityCapitalCashFlow }; 