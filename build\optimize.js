#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🚀 Node.js专用优化...\n');

function optimizeFile(filePath) {
  // 确保路径是绝对路径
  const absolutePath = path.resolve(__dirname, filePath);
  if (!fs.existsSync(absolutePath)) {
    console.log(`⚠️  文件不存在: ${absolutePath}`);
    return;
  }

  let content = fs.readFileSync(absolutePath, 'utf8');
  const originalSize = content.length;

  // 先将原文件重命名为 .dev.js 版本
  const devPath = absolutePath.replace('.js', '.dev.js');
  fs.writeFileSync(devPath, fs.readFileSync(absolutePath, 'utf8'), 'utf8');

  // 移除注释
  content = content.replace(/\/\/[^\r\n]*/g, '');
  content = content.replace(/\/\*[\s\S]*?\*\//g, '');
  
  // 移除showLogs相关代码
  content = content.replace(/,\s*showLogs\s*=\s*[^,)]+/g, '');
  content = content.replace(/if\s*\(\s*showLogs\s*\)[^}]*}/g, '');
  
  // 移除console调用
  content = content.replace(/console\.\w+\([^)]*\);?/g, '');
  
  // 压缩空白 
  content = content.replace(/\s+/g, ' ').trim();
  
  // 将压缩版本覆盖原文件（这样run.min.js就变成了run.js）
  fs.writeFileSync(absolutePath, content, 'utf8');
  
  const finalSize = content.length;
  const savings = ((originalSize - finalSize) / originalSize * 100).toFixed(1);
  
  console.log(`✅ ${path.basename(devPath)}: ${Math.round(originalSize/1024)}KB (开发版本)`);
  console.log(`✅ ${path.basename(absolutePath)}: ${Math.round(finalSize/1024)}KB (生产版本, 节省${savings}%)`);
}

// 优化文件
optimizeFile('../dist/finance-model.js');
optimizeFile('../dist/run.js');

console.log('\n🎉 Node.js优化完成！'); 