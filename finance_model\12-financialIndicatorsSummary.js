const { result } = require('lodash');
const { SUM, NPV, IRR } = require('./utils');

/**
 * 计算财务指标汇总表
 * @param {Object} params - 输入参数
 * @param {boolean} showLogs - 是否显示表格数据（可选，默认为false）
 * @returns {Object} - 财务指标汇总表结果
 */
function calculateFinancialIndicatorsSummary(params, showLogs = false) {
    const {
        operatingYears, // 项目运营年限
        discountRate, // 折现率
        projectTotalInvestment, // 项目总投资
        totalCostExpense, // 第N年总成本费用
        nonTaxOperatingIncome, // 第N年不含税营业收入
        netCashFlowBeforeIncomeTax, // 第N年所得税前净现金流量
        netCashFlowAfterIncomeTax, // 第N年所得税后净现金流量
        dynamicPaybackPeriod_presentValueBeforeTax, // 第N年动态回收期_税前现值
        dynamicPaybackPeriod_presentValueAfterTax, // 第N年动态回收期_税后现值
        dynamicPaybackPeriod_accumulatedNetCashFlowBeforeTax, // 第N年动态回收期_累计所得税前净现金流量
        dynamicPaybackPeriod_accumulatedNetCashFlowAfterTax, // 第N年动态回收期_累计所得税后净现金流量
        accumulatedNetCashFlowBeforeIncomeTax, // 第N年累计所得税前净现金流量
        accumulatedNetCashFlowAfterIncomeTax, // 第N年累计所得税后净现金流量
        capitalPretaxNetCashFlow, // 第N年资本金税前净现金流量
        capitalNetCashFlow, // 第N年资本金税后净现金流量
        presentValue, // 第N年现值
        accumulatedPresentValue, // 第N年累计现值
        equityCapital, // 资本金
        bankLoan, // 银行借款
        workingCapital, // 流动资金
        fixedAssetsStaticInvestment, // 固定资产静态投资
        constructionPeriodInterest, // 建设期利息
        hydrogenProduction, // 第N年制氢量
        installedCapacity, // 装机量
        interestAndTaxProfit, // 第N年息税前利润
        operatingCost, // 第N年经营成本
        salesTaxAndAdditions, // 第N年销售税金及附加
        totalProfit, // 第N年利润总额
        incomeTax, // 第N年所得税
        netProfit, // 第N年净利润
        vatPayable, // 第N年应缴税金
        vatSubsidyIncome, // 第N年补贴收入应税,
        gridElectricity, // 第N年上网电量
        greenHydrogenElectricity, // 第N年绿电制氢电量
        assetLiabilityRatio, // 第N年资产负债率
        hydrogenPriceWithTax, // 运营期氢气
        fixedPowerAssetsNoTax, //固定发电资产不含税
        powerDepreciation, // 第N年发电折旧费
        interestExpenseWithTax, // 第N年含税利息支出
        windTurbineOverhaulReplacementCost, // 第N年风电机组大修更换费用
        photovoltaicOverhaulReplacementCost, // 第N年光伏组件大修更换费用
        energyStorageOverhaulReplacementCost, // 第N年储能系统大修更换费用
        landRentalFee, // 第N年土地租赁费
        povertyAlleviationFee, // 第N年扶贫规费支出
        landTaxFee, // 第N年土地税费
        powerMaintenanceCost, // 第N年发电运维成本
        laborCost, // 第N年工资福利及劳保统筹和住房基金
        powerInsuranceCost, // 第N年保险费
        windTurbineMaterialsCost, // 第N年风电机组材料费
        photovoltaicMaterialsCost, // 第N年光伏组件材料费
        energyStorageMaterialsCost, // 第N年储能系统材料费
        powerOtherCost, // 第N年发电其他费用
        amortizationCost, // 第N年发电摊销费
        powerDiscountFactor, // 第N年发电折现系数
        salvageRate, // 第N年发电残值率
        discountedPowerGeneration, // 第N年发电量现值
        incomeTaxRate, // 所得税率
        depreciation, // 第N年发电折旧费
        fixedAssetsResidualValue, // 发电资产残值
        fixedAssetsNoTax, // 固定资产不含税
        predictedPowerGenerationWithPowerLimitation, // 第N年预计发电量衰减加限电
    } = params;

    // 确保参数有效
    const ensureNumber = (num, defaultVal = 0) => num !== undefined && num !== null ? Number(num) : defaultVal;
    const ensureArray = (arr, length) => {
        if (!arr || !Array.isArray(arr)) return new Array(length).fill(0);
        return arr.length >= length ? arr : [...arr, ...new Array(length - arr.length).fill(0)];
    };
    
    // 标准化数据
    const years = ensureNumber(operatingYears, 25) + 1; // +1 是因为包括建设期
    const installCapacity = ensureNumber(installedCapacity);
    const projectLife = ensureNumber(operatingYears, 25);
    const totalInvestment = ensureNumber(projectTotalInvestment);
    const staticInvestment = ensureNumber(fixedAssetsStaticInvestment);
    const interest = ensureNumber(constructionPeriodInterest);
    const working = ensureNumber(workingCapital);
    const capital = ensureNumber(equityCapital);
    const loan = ensureNumber(bankLoan);
    
    // 标准化数组
    const nonTaxIncomeArr = ensureArray(nonTaxOperatingIncome, years);
    const totalCostArr = ensureArray(totalCostExpense, years);
    const operatingCostArr = ensureArray(operatingCost, years);
    const hydrogenArr = ensureArray(hydrogenProduction, years);
    const gridPowerArr = ensureArray(gridElectricity, years);
    const selfPowerArr = ensureArray(greenHydrogenElectricity, years);
    const ebtInterestArr = ensureArray(interestAndTaxProfit, years);
    const salesTaxArr = ensureArray(salesTaxAndAdditions, years);
    const profitArr = ensureArray(totalProfit, years);
    const incomeTaxArr = ensureArray(incomeTax, years);
    const vatPayableArr = ensureArray(vatPayable, years);
    const netProfitArr = ensureArray(netProfit, years);
    const assetLiabilityRatioArr = ensureArray(assetLiabilityRatio, years);
    const beforeTaxCashFlowArr = ensureArray(netCashFlowBeforeIncomeTax, years);
    const afterTaxCashFlowArr = ensureArray(netCashFlowAfterIncomeTax, years);
    const equityCashFlowBeforeTaxArr = ensureArray(capitalPretaxNetCashFlow, years);
    const equityCashFlowAfterTaxArr = ensureArray(capitalNetCashFlow, years);
    const subsidyIncomeArr = ensureArray(vatSubsidyIncome, years);

    // 创建结果对象
    const results = {
        // 基本信息
        installedCapacity: installCapacity, // 装机规模(MW)
        operatingYears: projectLife, // 项目经营期(年)
        
        // 发电量
        averageAnnualGeneration: 0, // 年均发电量(万kW·h)
        totalGridElectricity: 0, // 上网总结算电量(万kW·h)
        averageAnnualGridElectricity: 0, // 年均上网结算电量(万kW·h)
        annualEffectiveUtilizationHours: 0, // 年有效利用小时数(小时)
        
        // 投资指标
        projectTotalInvestment: totalInvestment, // 项目总投资(万元)
        fixedAssetsStaticInvestment: staticInvestment, // 固定资产静态投资(万元)
        constructionPeriodInterest: interest, // 建设期利息(万元)
        workingCapital: working, // 流动资金(万元)
        staticInvestmentPerWatt: 0, // 单瓦静态总投资(元/W)
        dynamicInvestmentPerWatt: 0, // 单瓦动态总投资(元/W)
        investmentCostPerKWh: 0, // 度电投资成本(元/kWh)
        
        // 资金来源
        equityCapitalAmount: capital, // 项目资本金投入金额(万元)
        totalBankLoanAmount: loan, // 项目银行贷款总额(万元)
        
        // 经营指标
        totalSalesRevenue: 0, // 销售收入总额(万元)
        totalCostExpense: 0, // 总成本费用(万元)
        averageAnnualOperatingCost: 0, // 年平均运营成本(万元)
        totalGenerationCostPerUnit: 0, // 单位发电总成本(元/kWh)
        operatingCostPerKWh: 0, // 度电运营成本(元/kWh)
        totalSalesTaxSurcharges: 0, // 销售税金附加总额(万元)
        totalPowerGenerationProfit: 0, // 发电利润总额(万元)
        levelizedCostOfElectricity: 0, // 平准化度电成本LCOE(元/kWh)
        
        // 回收期指标
        paybackPeriodAfterTax_static: 0, // 税后投资回收期(静态)(年)
        paybackPeriodAfterTax_dynamic: 0, // 税后投资回收期(动态)(年)
        
        // 收益率指标
        projectInvestmentFIRR_beforeTax: 0, // 项目投资财务内部收益率_税前(%)
        projectInvestmentFIRR_afterTax: 0, // 项目投资财务内部收益率_税后(%)
        equityCapitalFIRR_beforeTax: 0, // 资本金财务内部收益率_税前(%)
        equityCapitalFIRR_afterTax: 0, // 资本金财务内部收益率_税后(%)
        
        // 其他财务指标
        hydrogenPrice: ensureNumber(hydrogenPriceWithTax), // 氢气价格(元/kg)
        projectInvestmentNPV_beforeTax: 0, // 项目投资财务税前净现值(万元)
        equityCapitalNPV_afterTax: 0, // 资本金财务税后净现值(万元)
        returnOnInvestment: 0, // 总投资收益率ROI(%)
        investmentTaxRate: 0, // 投资利税率(%)
        returnOnEquity: 0, // 项目资本金净利润率ROE(%)
        assetLiabilityRatio: 0, // 资产负债率(%)
        vatRefund50Percent: 0, // 增值税即征即退50%(万元)
        lcoe: 0,
        lcoh: 0,
        lcoh2: 0,
        lcoh_ori: 0,
    };

    // 计算总和
    const totalGridElectricity = SUM(gridPowerArr);
    const totalSelfUseElectricity = SUM(selfPowerArr);
    const totalElectricity = totalGridElectricity + totalSelfUseElectricity;
    const totalOperatingCost = SUM(operatingCostArr);
    const totalNonTaxIncome = SUM(nonTaxIncomeArr);
    const totalCostExpenseSum = SUM(totalCostArr);
    const totalProfitSum = SUM(profitArr);
    const totalSalesTax = SUM(salesTaxArr); // TODO: 需要修改
    const totalIncomeTax = SUM(incomeTaxArr);
    const totalVatPayable = SUM(vatPayableArr);
    const totalHydrogenProduction = SUM(hydrogenArr);
    const totalEbtInterest = SUM(ebtInterestArr);
    const totalNetProfit = SUM(netProfitArr);
    const totalVatSubsidy = SUM(subsidyIncomeArr);

    // 计算年均指标
    results.averageAnnualGeneration = totalSelfUseElectricity / projectLife || 0;
    results.totalGridElectricity = totalGridElectricity || 0;
    results.averageAnnualGridElectricity = totalGridElectricity / projectLife || 0;
    results.annualEffectiveUtilizationHours = results.averageAnnualGridElectricity / installCapacity * 10 || 0;
    
    // 计算投资指标
    results.staticInvestmentPerWatt = staticInvestment * 10000 / (installCapacity * 1000000) || 0;
    results.dynamicInvestmentPerWatt = (totalInvestment - working) * 10000 / (installCapacity * 1000000) || 0;
    results.investmentCostPerKWh = totalInvestment / totalGridElectricity || 0; //
    
    // 计算经营指标
    results.totalSalesRevenue = totalNonTaxIncome || 0;
    results.totalCostExpense = totalCostExpenseSum || 0;
    results.averageAnnualOperatingCost = totalOperatingCost / projectLife || 0;
    results.totalGenerationCostPerUnit = (totalCostExpenseSum / projectLife) / results.averageAnnualGeneration || 0;
    results.operatingCostPerKWh = results.averageAnnualOperatingCost / results.averageAnnualGeneration || 0;
    results.totalSalesTaxSurcharges = totalSalesTax || 0;
    results.totalPowerGenerationProfit = totalProfitSum || 0;
    
    // 计算回收期指标
    results.paybackPeriodAfterTax_static = calculateStaticPaybackPeriod(
        accumulatedNetCashFlowAfterIncomeTax,
        netCashFlowAfterIncomeTax
    );
    results.paybackPeriodAfterTax_dynamic = calculateDynamicPaybackPeriod(
        dynamicPaybackPeriod_accumulatedNetCashFlowAfterTax,
        dynamicPaybackPeriod_presentValueAfterTax
    );
    
    // 计算收益率指标
    results.projectInvestmentFIRR_beforeTax = IRR(beforeTaxCashFlowArr) || 0;
    results.projectInvestmentFIRR_afterTax = IRR(afterTaxCashFlowArr) || 0;
    results.equityCapitalFIRR_beforeTax = IRR(equityCashFlowBeforeTaxArr) || 0;
    results.equityCapitalFIRR_afterTax = IRR(equityCashFlowAfterTaxArr) || 0;
    
    // 计算其他财务指标
    results.projectInvestmentNPV_beforeTax = NPV(discountRate, beforeTaxCashFlowArr) || 0;
    results.equityCapitalNPV_afterTax = NPV(0.08, equityCashFlowAfterTaxArr) || 0; // 资本金财务税后净现值=NPV(8%,第N年资本金税后净现金流量)
    
    results.returnOnInvestment = (totalEbtInterest / projectLife) / totalInvestment;
    results.investmentTaxRate = ((totalProfitSum + totalSalesTax + totalIncomeTax + totalVatPayable) / projectLife) / totalInvestment || 0;
    results.returnOnEquity = (totalNetProfit / projectLife) / capital || 0;
    
    // 取第一年的资产负债率
    results.assetLiabilityRatio = assetLiabilityRatioArr[0] || 0;
    
    // 增值税即征即退
    results.vatRefund50Percent = totalVatSubsidy || 0;
    
    // lcoe
    // 发电资产不含税
    const fadianWithNoRate = fixedPowerAssetsNoTax; 

    // 发电运营折现率
    let fadianYunYingZheXian = 0;
    for (let i = 0; i <= operatingYears; i++) {
        // fadianYunYingZheXian += (第N年发电折旧费powerDepreciation + 第N年发电电费0 + 第N年含税利息支出interestExpenseWithTax + 第N年设备大修更换费用(windTurbineOverhaulReplacementCost+photovoltaicOverhaulReplacementCost+energyStorageOverhaulReplacementCost) + 第N年土地租赁费landRentalFee + 第N年扶贫规费支出povertyAlleviationFee + 第N年土地税费landTaxFee)
        // + （第N年维修费powerMaintenanceCost + 第N年工资福利及劳保统筹和住房基金laborCost + 第N年保险费powerInsuranceCost + 第N年水费0 + 第N年材料费(windTurbineMaterialsCost+photovoltaicMaterialsCost+energyStorageMaterialsCost) + 第N年运输成本0 + 第N年其他费用powerOtherCost + 第N年水资源税0)
        fadianYunYingZheXian += ((
            powerDepreciation[i] + 0 + interestExpenseWithTax[i]
            + (windTurbineOverhaulReplacementCost[i]+photovoltaicOverhaulReplacementCost[i]+energyStorageOverhaulReplacementCost[i])
            + landRentalFee[i] + povertyAlleviationFee[i] + landTaxFee[i]
        )
        + (
            powerMaintenanceCost[i] + laborCost[i] + powerInsuranceCost[i] + 0 + 
            (windTurbineMaterialsCost[i]+photovoltaicMaterialsCost[i]+energyStorageMaterialsCost[i])
            + 0 + powerOtherCost[i] + 0
        )
        - powerDepreciation[i] - amortizationCost[i]) *  powerDiscountFactor[i]
    }

    let fadianAllCost = 0;
    for (let i = 0; i <= operatingYears; i++) {
        // fadianYunYingZheXian += (第N年发电折旧费powerDepreciation + 第N年发电电费0 + 第N年含税利息支出interestExpenseWithTax + 第N年设备大修更换费用(windTurbineOverhaulReplacementCost+photovoltaicOverhaulReplacementCost+energyStorageOverhaulReplacementCost) + 第N年土地租赁费landRentalFee + 第N年扶贫规费支出povertyAlleviationFee + 第N年土地税费landTaxFee)
        // + （第N年维修费powerMaintenanceCost + 第N年工资福利及劳保统筹和住房基金laborCost + 第N年保险费powerInsuranceCost + 第N年水费0 + 第N年材料费(windTurbineMaterialsCost+photovoltaicMaterialsCost+energyStorageMaterialsCost) + 第N年运输成本0 + 第N年其他费用powerOtherCost + 第N年水资源税0)
        fadianAllCost += (
            powerDepreciation[i] + 0 + interestExpenseWithTax[i]
            + (windTurbineOverhaulReplacementCost[i]+photovoltaicOverhaulReplacementCost[i]+energyStorageOverhaulReplacementCost[i])
            + landRentalFee[i] + povertyAlleviationFee[i] + landTaxFee[i]
        )
        + (
            powerMaintenanceCost[i] + laborCost[i] + powerInsuranceCost[i] + 0 + 
            (windTurbineMaterialsCost[i]+photovoltaicMaterialsCost[i]+energyStorageMaterialsCost[i])
            + 0 + powerOtherCost[i] + 0
        )
    }

    // 发电减税现值
    let fadianJianShuiXianZhi = 0; 
    for (let i = 0; i <= operatingYears; i++) {
        fadianJianShuiXianZhi += (powerDepreciation[i] + amortizationCost[i]) * incomeTaxRate[i] *  powerDiscountFactor[i]
    }

    // 发电残值现值
    const fadianCanZhiXianZhi = (fixedPowerAssetsNoTax * salvageRate) * powerDiscountFactor[operatingYears]; 

    // 发电量现值
    const fadainliangXianZhi = SUM(discountedPowerGeneration); 
    const fadianliangAll = SUM(predictedPowerGenerationWithPowerLimitation)

    results.lcoe = (fadianWithNoRate + fadianYunYingZheXian - fadianJianShuiXianZhi - fadianCanZhiXianZhi) / fadainliangXianZhi
    results.lcoe_ori = fadianAllCost / fadianliangAll

    // lcoh
    // 总运营成本现值
    let yunyingZheXian = 0
    for (let i = 0; i <= operatingYears; i++) {
        yunyingZheXian += (totalCostExpense[i] - depreciation[i] - amortizationCost[i]) * powerDiscountFactor[i]
    }
    // 减税现值
    let jianshuiXianZhi = 0
    for (let i = 0; i <= operatingYears; i++) {
        jianshuiXianZhi += (depreciation[i] + amortizationCost[i]) * incomeTaxRate[i] *  powerDiscountFactor[i]
    }
    // 残值现值
    let canZhiXianZhi = fixedAssetsResidualValue[operatingYears] * powerDiscountFactor[operatingYears]

    // 产氢量现值
    let chanQingXianZhi = 0;
    for (let i = 0; i <= operatingYears; i++) {
        chanQingXianZhi += hydrogenProduction[i] * powerDiscountFactor[i]
    }

    results.lcoh = (fixedAssetsNoTax + yunyingZheXian - jianshuiXianZhi - canZhiXianZhi) / chanQingXianZhi * 0.08988
    results.lcoh2 = (fixedAssetsNoTax + yunyingZheXian) / chanQingXianZhi * 0.08988

    // lcoh_ori
    const totalH2 = SUM(hydrogenProduction) * 11.1259
    results.lcoh_ori = results.totalCostExpense / totalH2

    // 打印分类表格数据
    if (showLogs) {
        
        console.log('======================= 财务指标汇总表 =======================');
        
        // 基本信息
        console.log('\n-------------------- 基本信息 --------------------');
        console.table({
            '装机规模(MW)': results.installedCapacity,
            '项目经营期(年)': results.operatingYears
        });
        
        // 发电量信息
        console.log('\n-------------------- 发电量信息 --------------------');
        console.table({
            '年均发电量(万kW·h)': results.averageAnnualGeneration,
            '上网总结算电量(万kW·h)': results.totalGridElectricity,
            '年均上网结算电量(万kW·h)': results.averageAnnualGridElectricity,
            '年有效利用小时数(小时)': results.annualEffectiveUtilizationHours
        });
        
        // 投资指标
        console.log('\n-------------------- 投资指标 --------------------');
        console.table({
            '项目总投资(万元)': results.projectTotalInvestment,
            '固定资产静态投资(万元)': results.fixedAssetsStaticInvestment,
            '建设期利息(万元)': results.constructionPeriodInterest,
            '流动资金(万元)': results.workingCapital,
            '单瓦静态总投资(元/W)': results.staticInvestmentPerWatt,
            '单瓦动态总投资(元/W)': results.dynamicInvestmentPerWatt,
            '度电投资成本(元/kWh)': results.investmentCostPerKWh
        });
        
        // 资金来源
        console.log('\n-------------------- 资金来源 --------------------');
        console.table({
            '项目资本金投入金额(万元)': results.equityCapitalAmount,
            '项目银行贷款总额(万元)': results.totalBankLoanAmount
        });
        
        // 经营指标
        console.log('\n-------------------- 经营指标 --------------------');
        console.table({
            '销售收入总额(万元)': results.totalSalesRevenue,
            '总成本费用(万元)': results.totalCostExpense,
            '年平均运营成本(万元)': results.averageAnnualOperatingCost,
            '单位发电总成本(元/kWh)': results.totalGenerationCostPerUnit,
            '度电运营成本(元/kWh)': results.operatingCostPerKWh,
            '销售税金附加总额(万元)': results.totalSalesTaxSurcharges,
            '发电利润总额(万元)': results.totalPowerGenerationProfit,
            '平准化度电成本LCOE(元/kWh)': results.levelizedCostOfElectricity
        });
        
        // 回收期指标
        console.log('\n-------------------- 回收期指标 --------------------');
        console.table({
            '税后投资回收期(静态)(年)': results.paybackPeriodAfterTax_static,
            '税后投资回收期(动态)(年)': results.paybackPeriodAfterTax_dynamic
        });
        
        // 收益率指标
        console.log('\n-------------------- 收益率指标 --------------------');
        console.table({
            '项目投资财务内部收益率_税前(%)': results.projectInvestmentFIRR_beforeTax * 100,
            '项目投资财务内部收益率_税后(%)': results.projectInvestmentFIRR_afterTax * 100,
            '资本金财务内部收益率_税前(%)': results.equityCapitalFIRR_beforeTax * 100,
            '资本金财务内部收益率_税后(%)': results.equityCapitalFIRR_afterTax * 100
        });
        
        // 其他财务指标
        console.log('\n-------------------- 其他财务指标 --------------------');
        console.table({
            '氢气价格(元/kg)': results.hydrogenPrice,
            '项目投资财务税前净现值(万元)': results.projectInvestmentNPV_beforeTax,
            '资本金财务税后净现值(万元)': results.equityCapitalNPV_afterTax,
            '总投资收益率ROI(%)': results.returnOnInvestment * 100,
            '投资利税率(%)': results.investmentTaxRate * 100,
            '项目资本金净利润率ROE(%)': results.returnOnEquity * 100,
            '资产负债率(%)': results.assetLiabilityRatio * 100,
            '增值税即征即退50%(万元)': results.vatRefund50Percent,
            lcoe: results.lcoe,
            lcoh: results.lcoh,
            lcoh2: results.lcoh2,
            lcoh_ori: results.lcoh_ori,
            lcoe_ori: results.lcoe_ori
        });
    }

    return results;
}

/**
 * 计算静态回收期
 * @param {Array} accumulatedCashFlow - 累计现金流量数组
 * @param {Array} annualCashFlow - 年度现金流量数组
 * @returns {Number} - 静态回收期（年）
 */
function calculateStaticPaybackPeriod(accumulatedCashFlow, annualCashFlow) {
    if (!accumulatedCashFlow || !Array.isArray(accumulatedCashFlow) || 
        !annualCashFlow || !Array.isArray(annualCashFlow)) {
        return 0;
    }
    
    // 找到累计现金流量首次转正的年份
    let recoveryYear = 0;
    for (let i = 1; i < accumulatedCashFlow.length; i++) {
        if (accumulatedCashFlow[i] >= 0 && accumulatedCashFlow[i-1] < 0) {
            // 插值计算精确回收期 = 上一年份 + |上一年累计现金流量| / 当年现金流量
            recoveryYear = i - 1 + Math.abs(accumulatedCashFlow[i-1]) / (annualCashFlow[i] || 1);
            break;
        }
    }
    return recoveryYear || 0;
}

/**
 * 计算动态回收期
 * @param {Array} accumulatedPresentValue - 累计现值数组
 * @param {Array} annualPresentValue - 年度现值数组
 * @returns {Number} - 动态回收期（年）
 */
function calculateDynamicPaybackPeriod(accumulatedPresentValue, annualPresentValue) {
    if (!accumulatedPresentValue || !Array.isArray(accumulatedPresentValue) || 
        !annualPresentValue || !Array.isArray(annualPresentValue)) {
        return 0;
    }
    
    // 找到累计现值首次转正的年份
    let recoveryYear = 0;
    for (let i = 1; i < accumulatedPresentValue.length; i++) {
        if (accumulatedPresentValue[i] >= 0 && accumulatedPresentValue[i-1] < 0) {
            // 插值计算精确回收期 = 上一年份 + |上一年累计现值| / 当年现值
            recoveryYear = i - 1 + Math.abs(accumulatedPresentValue[i-1]) / (annualPresentValue[i] || 1);
            break;
        }
    }
    return recoveryYear || 0;
}

module.exports = { calculateFinancialIndicatorsSummary }; 