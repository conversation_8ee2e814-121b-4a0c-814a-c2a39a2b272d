// 财务测算模型测试文件
const { cloneDeep } = require('lodash')
const { getFinanceData } = require('../finance_model/run')
// const { getFinanceData } = require('../dist/run.js')
const fs = require('fs')

const testInputs = {
    "VATRate": 0.13,
    "alkBuildingRatio": 0.314444,
    "alkCapacity": 320,
    "alkEPC": 3.2,
    "alkEquipmentRatio": 0.571003,
    "alkInstallRatio": 0.0803617,
    "alkOthersRatio": 0.03419,
    "alkStoreBuildingRatio": 0.314444,
    "alkStoreCapacity": 0,
    "alkStoreEPC": 2500,
    "alkStoreEquipmentRatio": 0.571003,
    "alkStoreInstallRatio": 0.0803617,
    "alkStoreOthersRatio": 0.03419,
    "baseLoanRate": 0.035,
    "batBuildingRatio": 0.05793,
    "batCapacity": 50,
    "batEPC": 1.25,
    "batEquipmentRatio": 0.8032,
    "batInstallRatio": 0.05573,
    "batOthersRatio": 0.08311,
    "capfirstYearGreenPower": 21378,
    "capfirstYearGridHydrogenProduction": 24.3,
    "capfirstYearHydrogenElectricity": 17602,
    "capfirstYearHydrogenProduction": 333.5,
    "capfirstYearUpGridPower": 3420.48,
    "cityConstructionAndMaintenanceTaxRate": 0.05,
    "config": {
        "initInfo": {
            "totalCost": []
        }
    },
    "customerName": "中煤绿能",
    "desc": "并网",
    "discountRate": 0.05,
    "educationSurchargeRate": 0.03,
    "electricityFeeVATRate": 0.13,
    "electrolyzerBuilding": 16298,
    "electrolyzerDepreciationYears": 20,
    "electrolyzerEquipment": 55568,
    "electrolyzerInstallation": 18975,
    "electrolyzerInsuranceRate": 0,
    "electrolyzerMaintCostGrowthRateYears12plus": 0.01,
    "electrolyzerMaintCostGrowthRateYears7to11": 0.01,
    "electrolyzerMaintCostRateYear2": 0.0037,
    "electrolyzerMajorOverhaulReplacementRate": 0.3,
    "electrolyzerMaterialsCostAnnualGrowthRate": 0.015,
    "electrolyzerMaterialsCostProvisionRate": 0.003,
    "electrolyzerOtherCostGrowthRate": 0.03,
    "electrolyzerOtherCostUnitPrice": 0.4,
    "electrolyzerOthers": 14645,
    "electrolyzerPowerConsumption": 4.3,
    "energyStorageBuilding": 0,
    "energyStorageDepreciationYears": 20,
    "energyStorageEquipment": 3020,
    "energyStorageInstallation": 69.84,
    "energyStorageInsuranceRate": 0.0005,
    "energyStorageMaintCostGrowthRateYears12plus": 0.02,
    "energyStorageMaintCostGrowthRateYears7to11": 0.015,
    "energyStorageMaintCostRateYear2": 0.004,
    "energyStorageMajorOverhaulReplacementRate": 0.6,
    "energyStorageMaterialsCostAnnualGrowthRate": 0.015,
    "energyStorageMaterialsCostProvisionRate": 0.003,
    "energyStorageOtherCostGrowthRate": 0.03,
    "energyStorageOtherCostUnitPrice": 0.5,
    "energyStorageOthers": 0,
    "equityRatio": 0.3,
    "financingRatioBase": 0.7,
    "firstYearDownGridPower": 0,
    "fixedAssetsFinancialFactor": 1,
    "genH2ConsumeWater": 2,
    "gridElectricityPriceNoTax": 0.22,
    "gridElectricityPriceWithTax": 0.2829,
    "gridHydrogenElectricityPriceNoTax": 0.4389,
    "hydrogenPriceNoTax": 14.6,
    "hydrogenPriceWithTax": 16.5,
    "hydrogenProductionCostSensitivityFactor": 1,
    "hydrogenStorageBuilding": 0,
    "hydrogenStorageDepreciationYears": 20,
    "hydrogenStorageEquipment": 0,
    "hydrogenStorageInstallation": 0,
    "hydrogenStorageInsuranceRate": 0.0005,
    "hydrogenStorageMaintCostGrowthRateYears12plus": 0.01,
    "hydrogenStorageMaintCostGrowthRateYears7to11": 0.01,
    "hydrogenStorageMaintCostRateYear2": 0.005,
    "hydrogenStorageMajorOverhaulReplacementRate": 0.3,
    "hydrogenStorageMaterialsCostAnnualGrowthRate": 0.015,
    "hydrogenStorageMaterialsCostProvisionRate": 0.003,
    "hydrogenStorageOtherCostGrowthRate": 0.03,
    "hydrogenStorageOtherCostUnitPrice": 0.1,
    "hydrogenStorageOthers": 0,
    "incomeTaxRate": 0.25,
    "installationTaxRate": 0.09,
    "installedCapacity": 400,
    "isFromCapacity": false,
    "loanTerm": 15,
    "localEducationSurchargeRate": 0.03,
    "maintenanceCostVATRate": 0.13,
    "managerCount": 25,
    "managerMonthlySalary": 1.04,
    "materialsCostVATRate": 0.13,
    "operatingYears": 20,
    "oxygenPriceNoTax": 0,
    "oxygenPriceWithTax": 0,
    "photovoltaicBuilding": 26682,
    "photovoltaicDepreciationYears": 20,
    "photovoltaicEquipment": 56953,
    "photovoltaicInstallation": 29428.16,
    "photovoltaicInsuranceRate": 0.0005,
    "photovoltaicLandRentalFeeGrowthRate": 0.003,
    "photovoltaicLandRentalFeeYear2": 202.5,
    "photovoltaicLandTaxAnnual": 5.85828,
    "photovoltaicMaintCostGrowthRateYears12plus": 0.02,
    "photovoltaicMaintCostGrowthRateYears7to11": 0.015,
    "photovoltaicMaintCostRateYear2": 0.004,
    "photovoltaicMajorOverhaulReplacementRate": 0.3,
    "photovoltaicMaterialsCostAnnualGrowthRate": 0.015,
    "photovoltaicMaterialsCostProvisionRate": 0.003,
    "photovoltaicOtherCostGrowthRate": 0.03,
    "photovoltaicOtherCostUnitPrice": 1.5,
    "photovoltaicOthers": 31993,
    "powerGenerationCostSensitivityFactor": 1,
    "powerToHydrogenRatio": 0.71,
    "projectChangeFactor": 1,
    "projectName": "乌审旗风光一体化制氢项目",
    "projectScene": [
        1,
        1,
        1,
        1,
        1,
        0
    ],
    "publicLoadCapacity": 0,
    "publicLoadHour": 8000,
    "pvBuildingRatio": 0.05793,
    "pvCapacity": 497,
    "pvEPC": 3.5,
    "pvEquipmentRatio": 0.8032,
    "pvFirstYearPowerGenerationHour": 1622,
    "pvInstallRatio": 0.05573,
    "pvLandUnitPrice": 0.008425,
    "pvLandUnitPriceRate": 0.015,
    "pvOthersRatio": 0.08311,
    "rateChangeCoefficient": 1,
    "revenueItemVATRate": 0.13,
    "salaryAnnualGrowthRate": 0,
    "salvageRate": 0.05,
    "selfPowerPlantElectricityPriceNoTax": 0,
    "selfPowerPlantElectricityPriceWithTax": 0.1814,
    "shareholderDividendRatio": 0,
    "socialSecurityAndWelfareFactor": 0,
    "statutorySurplusReserveFundRate": 0.1,
    "technicianCount": 34,
    "technicianMonthlySalary": 1.04,
    "totalCostSensitivityFactor": 1,
    "transportDistance": 0,
    "transportationFeeVATRate": 0.09,
    "transportationRateNoTax": 0,
    "waterFeeVATRate": 0.09,
    "waterPriceNoTax": 2.2,
    "waterResourceTaxUnitPrice": 0.7,
    "windBuildingRatio": 0.05793,
    "windCapacity": 535.5,
    "windEPC": 3.6,
    "windEquipmentRatio": 0.8032,
    "windFirstYearPowerGenerationHour": 2381.6,
    "windInstallRatio": 0.05573,
    "windLandUnitPrice": 0.008425,
    "windOthersRatio": 0.08311,
    "windTurbineBuilding": 25347,
    "windTurbineDepreciationYears": 20,
    "windTurbineEquipment": 118852,
    "windTurbineInstallation": 42060,
    "windTurbineInsuranceRate": 0.0005,
    "windTurbineLandRentalFeeGrowthRate": 0.01,
    "windTurbineLandRentalFeeYear2": 135,
    "windTurbineLandTaxAnnual": 3.90552,
    "windTurbineMaintCostGrowthRateYears12plus": 0.02,
    "windTurbineMaintCostGrowthRateYears7to11": 0.015,
    "windTurbineMaintCostRateYear2": 0.004,
    "windTurbineMajorOverhaulReplacementRate": 0.3,
    "windTurbineMaterialsCostAnnualGrowthRate": 0.015,
    "windTurbineMaterialsCostProvisionRate": 0.003,
    "windTurbineOtherCostGrowthRate": 0.03,
    "windTurbineOtherCostUnitPrice": 1,
    "windTurbineOthers": 13691,
    "workerCount": 28,
    "workerMonthlySalary": 1.04,
    "yearNDegradationPercentage": 0.0055,
    "yearTwoDegradationPercentage": 0.02
}

const result = getFinanceData(testInputs, true)

// console.log('R:', result)
fs.writeFileSync('./test/result_7.28.json', JSON.stringify(result, null, 2))


