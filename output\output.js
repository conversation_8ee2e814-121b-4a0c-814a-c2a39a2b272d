// 注释格式：param: value // 类型：type，单位：unit，说明

const result = {
  // ===== 0. 关键指标表 =====
  "keyIndicators": {
      "loanRate": 0.031, // 类型：number，单位：无量纲，借款利率（基础借款利率 * 利率变化系数）
      "totalEducationSurchargeRate": 0.05 // 类型：number，单位：无量纲，总教育费附加税率（教育费附加税率 + 地方教育费附加税率）
  },

  // ===== 1. 固定资产投资估算表 =====
  "fixedAssetsInvestmentEstimation": {
      "mechanicalAndElectricalEquipment": 38033.51112, // 类型：number，单位：万元，机电设备及安装工程
      "constructionWorks": 5162.0724, // 类型：number，单位：万元，建筑工程（土建工程 * 财务测算固定资产敏感系数）
      "otherExpenses": 3403.294000000001, // 类型：number，单位：万元，其他费用（其他费用合计 * 财务测算固定资产敏感系数）
      "fixedAssetsStaticInvestment": 46598.87752, // 类型：number，单位：万元，固定资产静态投资（机电设备及安装工程 + 建筑工程 + 其他费用）
      "workingCapital": 300, // 类型：number，单位：万元，流动资金（装机量 * 30 / 10）
      "constructionPeriodInterest": 514.*************, // 类型：number，单位：万元，建设期利息
      "projectTotalInvestment": 47413.***********, // 类型：number，单位：万元，项目总投资（固定资产静态投资 + 建设期利息 + 流动资金）
      "fixedPowerAssetsNoTax": 33232.72251278721, // 类型：number，单位：万元，固定发电资产不含税（风机 + 光伏 + 储能）
      "fixedHydrogenAssetsNoTax": 9170.845189275447, // 类型：number，单位：万元，固定制氢资产不含税（电解槽 + 储罐）
      "fixedAssetsNoTax": 42403.56770206265, // 类型：number，单位：万元，固定资产不含税
      "fixedPowerAssetsWithTax": 36998.89, // 类型：number，单位：万元，固定发电资产含税
      "fixedHydrogenAssetsWithTax": 9599.98752, // 类型：number，单位：万元，固定制氢资产含税
      "fixedAssetsWithTax": 46598.87752, // 类型：number，单位：万元，固定资产含税
      "equipmentAndInstallationNoTax": 33749.993028821955, // 类型：number，单位：万元，设备及安装不含税
      "powerEquipmentAndInstallationNoTax": 28191.221320126657, // 类型：number，单位：万元，发电设备及安装不含税
      "powerEquipmentAndInstallationWithTax": 31780.41, // 类型：number，单位：万元，发电设备及安装含税
      "hydrogenEquipmentAndInstallationNoTax": 5558.7717086953, // 类型：number，单位：万元，制氢设备及安装不含税
      "hydrogenEquipmentAndInstallationWithTax": 6253.10112, // 类型：number，单位：万元，制氢设备及安装含税
      "equipmentPurchaseCost": 35200.0288, // 类型：number，单位：万元，设备购置费
      "installationCost": 2833.48232, // 类型：number，单位：万元，安装工程费
      "windTurbineEquipmentAndInstallationNoTax": 16457.577851749615, // 类型：number，单位：万元，风机设备及安装不含税
      "photovoltaicEquipmentAndInstallationNoTax": 9752.638726962738, // 类型：number，单位：万元，光伏设备及安装不含税
      "energyStorageEquipmentAndInstallationNoTax": 1981.0047414143057, // 类型：number，单位：万元，储能设备及安装不含税
      "electrolyzerEquipmentAndInstallationNoTax": 5558.7717086953, // 类型：number，单位：万元，电解槽设备及安装不含税
      "hydrogenStorageEquipmentAndInstallationNoTax": 0, // 类型：number，单位：万元，储罐设备及安装不含税
      "fixedWindTurbineAssetsNoTax": 19400.72449395145, // 类型：number，单位：万元，固定风机资产不含税
      "fixedPhotovoltaicAssetsNoTax": 11496.725626045307, // 类型：number，单位：万元，固定光伏资产不含税
      "fixedEnergyStorageAssetsNoTax": 2335.2723927904526, // 类型：number，单位：万元，固定储能资产不含税
      "fixedElectrolyzerAssetsNoTax": 9170.845189275447, // 类型：number，单位：万元，固定电解槽资产不含税
      "fixedHydrogenStorageAssetsNoTax": 0, // 类型：number，单位：万元，固定储罐资产不含税
      "windTurbineEquipmentAndInstallationWithTax": 18552.888, // 类型：number，单位：万元，风机设备及安装含税
      "photovoltaicEquipmentAndInstallationWithTax": 10994.304, // 类型：number，单位：万元，光伏设备及安装含税
      "energyStorageEquipmentAndInstallationWithTax": 2233.2180000000003, // 类型：number，单位：万元，储能设备及安装含税
      "electrolyzerEquipmentAndInstallationWithTax": 6253.10112, // 类型：number，单位：万元，电解槽设备及安装含税
      "hydrogenStorageEquipmentAndInstallationWithTax": 0, // 类型：number，单位：万元，储罐设备及安装含税
      "fixedWindTurbineAssetsWithTax": 21599.352, // 类型：number，单位：万元，固定风机资产含税
      "fixedPhotovoltaicAssetsWithTax": 12799.616000000002, // 类型：number，单位：万元，固定光伏资产含税
      "fixedEnergyStorageAssetsWithTax": 2599.9220000000005, // 类型：number，单位：万元，固定储能资产含税
      "fixedElectrolyzerAssetsWithTax": 9599.98752, // 类型：number，单位：万元，固定电解槽资产含税
      "fixedHydrogenStorageAssetsWithTax": 0 // 类型：number，单位：万元，固定储罐资产含税
  },

  // ===== 2. 工程总概算表 =====
  "projectOverallBudget": {
      "mechanicalAndElectricalEquipment": 38033.51112, // 类型：number，单位：万元，机电设备及安装工程
      "constructionWorks": 5162.0724, // 类型：number，单位：万元，建筑工程
      "otherExpenses": 3403.294000000001, // 类型：number，单位：万元，其他费用
      "staticInvestment": 46598.87752, // 类型：number，单位：万元，静态投资（固定资产静态投资）
      "constructionPeriodSubsidy": 0, // 类型：number，单位：万元，建设期补助
      "constructionInvestment": 46598.87752, // 类型：number，单位：万元，建设投资（静态投资 - 建设期补助）
      "constructionPeriodInterest": 514.*************, // 类型：number，单位：万元，建设期利息
      "totalProjectInvestment": 47113.***********, // 类型：number，单位：万元，工程总投资（基本预备费 + 静态投资 + 价差预备费 + 建设期利息）
      "dynamicInvestmentPerWatt": 4.****************, // 类型：number，单位：元/W，单瓦动态总投资
      "staticInvestmentPerWatt": 4.********* // 类型：number，单位：元/W，单瓦静态总投资
  },

  // ===== 3. 融资计划表 =====
  "financingPlan": {
      "projectTotalInvestment": 47413.***********, // 类型：number，单位：万元，项目总投资
      "bankLoan": 33189.***********, // 类型：number，单位：万元，银行借款
      "equityCapital": 14223.*********** // 类型：number，单位：万元，资本金
  },

  // ===== 4. 投资计划与资金筹措表 =====
  "investmentPlanAndFundRaising": {
      "projectTotalInvestment": 47413.***********, // 类型：number，单位：万元，项目总投资
      "fixedAssetsStaticInvestment": 46598.87752, // 类型：number，单位：万元，固定资产静态投资
      "foreignInvestment": 0, // 类型：number，单位：万元，外商投资
      "domesticInvestment": 46598.87752, // 类型：number，单位：万元，国内投资
      "constructionPeriodInterest": 514.*************, // 类型：number，单位：万元，建设期利息
      "foreignInterestDuringConstruction": 0, // 类型：number，单位：万元，外商建设期利息
      "domesticInterestDuringConstruction": 514.*************, // 类型：number，单位：万元，国内建设期利息
      "workingCapital": 300, // 类型：number，单位：万元，流动资金
      "fundRaising": 47413.***********, // 类型：number，单位：万元，资金筹措
      "equityCapital": 14223.***********, // 类型：number，单位：万元，资本金
      "bankLoan": 33189.***********, // 类型：number，单位：万元，银行借款
      "longTermLoan": 33189.***********, // 类型：number，单位：万元，长期借款
      "domesticLoan": 32674.************, // 类型：number，单位：万元，国内借款
      "foreignLoan": 0, // 类型：number，单位：万元，外商借款
      "constructionPeriodInterestLoan": 514.*************, // 类型：number，单位：万元，建设期利息借款
      "workingCapitalLoan": 0 // 类型：number，单位：万元，流动资金借款
  },

  // ===== 5. 年度制氢及上网电量表 =====
  "annualHydrogenAndGridPower": {
      "yearDegradationPercentage": [], // 类型：array，单位：无量纲，第N年衰减百分值
      "pvFirstYearPowerGenerationHour": 1761, // 类型：number，单位：小时，首年发电小时数
      "windFirstYearPowerGenerationHour": 2761, // 类型：number，单位：小时，首年发电小时数
      "componentDegradationEfficiency": [], // 类型：array，单位：无量纲，第N年组件衰减效率
      "powerLimitationRate": [], // 类型：array，单位：无量纲，第N年限电率
      "powerGenerationRate": [], // 类型：array，单位：无量纲，第N年发电率
      "predictedPowerGenerationAfterDegradation": [], // 类型：array，单位：万度，第N年预计发电量衰减后
      "predictedPowerGenerationWithPowerLimitation": [], // 类型：array，单位：万度，第N年预计发电量衰减加限电
      "gridElectricity": [], // 类型：array，单位：万度，第N年上网电量
      "greenHydrogenElectricity": [], // 类型：array，单位：万度，第N年绿电制氢电量
      "powerDiscountFactor": [], // 类型：array，单位：无量纲，第N年发电折现系数
      "discountedPowerGeneration": [], // 类型：array，单位：万度，第N年发电量折现值
      "gridHydrogenElectricity": [], // 类型：array，单位：万度，第N年下网制氢电量
      "hydrogenProduction": [], // 类型：array，单位：万公斤，第N年制氢量
      "oxygenProduction": [], // 类型：array，单位：万公斤，第N年制氧量
      "waterConsumption": [], // 类型：array，单位：万吨，第N年耗水量
      "greenPowerRatioAfterYears": [] // 类型：array，单位：无量纲，第N年绿电衰减后可用比例
  },

  // ===== 6. 还本付息计算表 =====
  "loanRepaymentSchedule": {
      "loanAmount": [], // 类型：array，单位：万元，第N年借款金额
      "principalRepayment": [], // 类型：array，单位：万元，第N年还本金额
      "interestPayment": [], // 类型：array，单位：万元，第N年付息金额
      "totalRepayment": [], // 类型：array，单位：万元，第N年还本付息合计
      "remainingPrincipal": [], // 类型：array，单位：万元，第N年剩余本金
      "interestExpenseWithTax": [], // 类型：array，单位：万元，第N年含税利息支出
      "interestExpenseNoTax": [] // 类型：array，单位：万元，第N年不含税利息支出
  },

  // ===== 7. 总成本费用表 =====
  "totalCostAndExpenses": {
      // 发电侧成本
      "powerDepreciation": [], // 类型：array，单位：万元，第N年发电折旧费
      "windTurbineDepreciation": [], // 类型：array，单位：万元，第N年风机折旧费
      "photovoltaicDepreciation": [], // 类型：array，单位：万元，第N年光伏折旧费
      "energyStorageDepreciation": [], // 类型：array，单位：万元，第N年储能折旧费
      "windTurbineMaintenanceCost": [], // 类型：array，单位：万元，第N年风机维修费
      "photovoltaicMaintenanceCost": [], // 类型：array，单位：万元，第N年光伏维修费
      "energyStorageMaintenanceCost": [], // 类型：array，单位：万元，第N年储能维修费
      "windTurbineOverhaulReplacementCost": [], // 类型：array，单位：万元，第N年风机大修更换费用
      "photovoltaicOverhaulReplacementCost": [], // 类型：array，单位：万元，第N年光伏大修更换费用
      "energyStorageOverhaulReplacementCost": [], // 类型：array，单位：万元，第N年储能大修更换费用
      "landRentalFee": [], // 类型：array，单位：万元，第N年土地租赁费
      "povertyAlleviationFee": [], // 类型：array，单位：万元，第N年扶贫规费支出
      "landTaxFee": [], // 类型：array，单位：万元，第N年土地税费
      "powerMaintenanceCost": [], // 类型：array，单位：万元，第N年发电运维成本
      "laborCost": [], // 类型：array，单位：万元，第N年工资福利及劳保统筹和住房基金
      "powerInsuranceCost": [], // 类型：array，单位：万元，第N年发电保险费
      "windTurbineMaterialsCost": [], // 类型：array，单位：万元，第N年风机材料费
      "photovoltaicMaterialsCost": [], // 类型：array，单位：万元，第N年光伏材料费
      "energyStorageMaterialsCost": [], // 类型：array，单位：万元，第N年储能材料费
      "powerOtherCost": [], // 类型：array，单位：万元，第N年发电其他费用
      "amortizationCost": [], // 类型：array，单位：万元，第N年发电摊销费
      "powerGenerationCost": [], // 类型：array，单位：万元，第N年发电成本
      
      // 制氢侧成本
      "hydrogenDepreciation": [], // 类型：array，单位：万元，第N年制氢折旧费
      "electrolyzerDepreciation": [], // 类型：array，单位：万元，第N年电解槽折旧费
      "hydrogenStorageDepreciation": [], // 类型：array，单位：万元，第N年储罐折旧费
      "electrolyzerMaintenanceCost": [], // 类型：array，单位：万元，第N年电解槽维修费
      "hydrogenStorageMaintenanceCost": [], // 类型：array，单位：万元，第N年储罐维修费
      "electrolyzerOverhaulReplacementCost": [], // 类型：array，单位：万元，第N年电解槽大修更换费用
      "hydrogenStorageOverhaulReplacementCost": [], // 类型：array，单位：万元，第N年储罐大修更换费用
      "hydrogenMaintenanceCost": [], // 类型：array，单位：万元，第N年制氢运维成本
      "hydrogenInsuranceCost": [], // 类型：array，单位：万元，第N年制氢保险费
      "electrolyzerMaterialsCost": [], // 类型：array，单位：万元，第N年电解槽材料费
      "hydrogenStorageMaterialsCost": [], // 类型：array，单位：万元，第N年储罐材料费
      "hydrogenOtherCost": [], // 类型：array，单位：万元，第N年制氢其他费用
      "waterCost": [], // 类型：array，单位：万元，第N年水费
      "waterResourceTax": [], // 类型：array，单位：万元，第N年水资源税
      "selfPowerPlantElectricityCost": [], // 类型：array，单位：万元，第N年自备电厂电费
      "gridHydrogenElectricityCost": [], // 类型：array，单位：万元，第N年下网电费
      "transportationCost": [], // 类型：array，单位：万元，第N年运输费
      "hydrogenProductionCost": [], // 类型：array，单位：万元，第N年制氢成本
      
      // 总成本
      "interestExpenseWithTax": [], // 类型：array，单位：万元，第N年含税利息支出
      "totalCostExpense": [], // 类型：array，单位：万元，第N年总成本费用
      "operatingCost": [] // 类型：array，单位：万元，第N年经营成本
  },

  // ===== 8. 利润和利润分配表 =====
  "profitAndProfitDistribution": {
      "nonTaxOperatingIncome": [], // 类型：array，单位：万元，第N年不含税营业收入
      "hydrogenRevenueNoTax": [], // 类型：array，单位：万元，第N年氢气销售收入不含税
      "oxygenRevenueNoTax": [], // 类型：array，单位：万元，第N年氧气销售收入不含税
      "gridElectricityRevenueNoTax": [], // 类型：array，单位：万元，第N年上网电费收入不含税
      "vatSubsidyIncome": [], // 类型：array，单位：万元，第N年补贴收入应税
      "totalCostExpense": [], // 类型：array，单位：万元，第N年总成本费用
      "operatingCost": [], // 类型：array，单位：万元，第N年经营成本
      "interestExpenseWithTax": [], // 类型：array，单位：万元，第N年含税利息支出
      "interestAndTaxProfit": [], // 类型：array，单位：万元，第N年息税前利润
      "salesTaxAndAdditions": [], // 类型：array，单位：万元，第N年销售税金及附加
      "totalProfit": [], // 类型：array，单位：万元，第N年利润总额
      "incomeTax": [], // 类型：array，单位：万元，第N年所得税
      "netProfit": [], // 类型：array，单位：万元，第N年净利润
      "statutorySurplusReserveFund": [], // 类型：array，单位：万元，第N年法定盈余公积金
      "undistributedProfit": [], // 类型：array，单位：万元，第N年未分配利润
      "shareholderDividends": [], // 类型：array，单位：万元，第N年股东分红
      "vatPayable": [] // 类型：array，单位：万元，第N年应缴税金
  },

  // ===== 9. 项目投资现金流量表 =====
  "projectInvestmentCashFlow": {
      "fixedAssetsInvestment": [], // 类型：array，单位：万元，第N年固定资产投资
      "workingCapitalInvestment": [], // 类型：array，单位：万元，第N年流动资金投资
      "totalInvestment": [], // 类型：array，单位：万元，第N年投资合计
      "nonTaxOperatingIncome": [], // 类型：array，单位：万元，第N年不含税营业收入
      "vatSubsidyIncome": [], // 类型：array，单位：万元，第N年补贴收入应税
      "operatingCost": [], // 类型：array，单位：万元，第N年经营成本
      "salesTaxAndAdditions": [], // 类型：array，单位：万元，第N年销售税金及附加
      "vatPayable": [], // 类型：array，单位：万元，第N年应缴税金
      "netCashFlowBeforeIncomeTax": [], // 类型：array，单位：万元，第N年所得税前净现金流量
      "incomeTax": [], // 类型：array，单位：万元，第N年所得税
      "netCashFlowAfterIncomeTax": [], // 类型：array，单位：万元，第N年所得税后净现金流量
      "presentValueFactor": [], // 类型：array，单位：无量纲，第N年现值系数
      "presentValueBeforeTax": [], // 类型：array，单位：万元，第N年税前现值
      "presentValueAfterTax": [], // 类型：array，单位：万元，第N年税后现值
      "accumulatedNetCashFlowBeforeIncomeTax": [], // 类型：array，单位：万元，第N年累计所得税前净现金流量
      "accumulatedNetCashFlowAfterIncomeTax": [], // 类型：array，单位：万元，第N年累计所得税后净现金流量
      "dynamicPaybackPeriod_presentValueBeforeTax": [], // 类型：array，单位：万元，第N年动态回收期_税前现值
      "dynamicPaybackPeriod_presentValueAfterTax": [], // 类型：array，单位：万元，第N年动态回收期_税后现值
      "dynamicPaybackPeriod_accumulatedNetCashFlowBeforeTax": [], // 类型：array，单位：万元，第N年动态回收期_累计所得税前净现金流量
      "dynamicPaybackPeriod_accumulatedNetCashFlowAfterTax": [] // 类型：array，单位：万元，第N年动态回收期_累计所得税后净现金流量
  },

  // ===== 10. 资本金财务现金流量表 =====
  "equityCapitalCashFlow": {
      "equityCapitalInvestment": [], // 类型：array，单位：万元，第N年资本金投入
      "bankLoanInflow": [], // 类型：array，单位：万元，第N年银行借款流入
      "principalRepayment": [], // 类型：array，单位：万元，第N年还本
      "interestPayment": [], // 类型：array，单位：万元，第N年付息
      "capitalPretaxNetCashFlow": [], // 类型：array，单位：万元，第N年资本金税前净现金流量
      "incomeTax": [], // 类型：array，单位：万元，第N年所得税
      "capitalNetCashFlow": [], // 类型：array，单位：万元，第N年资本金税后净现金流量
      "presentValue": [], // 类型：array，单位：万元，第N年现值
      "accumulatedPresentValue": [] // 类型：array，单位：万元，第N年累计现值
  },

  // ===== 11. 财务计划现金流量表 =====
  "financialPlanCashFlow": {
      "operatingActivitiesNetCashFlow": [], // 类型：array，单位：万元，第N年经营活动净现金流量
      "investmentActivitiesNetCashFlow": [], // 类型：array，单位：万元，第N年投资活动净现金流量
      "financingActivitiesNetCashFlow": [], // 类型：array，单位：万元，第N年筹资活动净现金流量
      "netCashFlowIncrease": [], // 类型：array，单位：万元，第N年现金净增加额
      "beginningCashBalance": [], // 类型：array，单位：万元，第N年期初现金余额
      "endingCashBalance": [] // 类型：array，单位：万元，第N年期末现金余额
  },

  // ===== 12. 资产负债表 =====
  "balanceSheet": {
      "totalAssets": [], // 类型：array，单位：万元，第N年资产总计
      "currentAssets": [], // 类型：array，单位：万元，第N年流动资产
      "cashAndEquivalents": [], // 类型：array，单位：万元，第N年货币资金
      "inventory": [], // 类型：array，单位：万元，第N年存货
      "nonCurrentAssets": [], // 类型：array，单位：万元，第N年非流动资产
      "fixedAssetsOriginalValue": [], // 类型：array，单位：万元，第N年固定资产原值
      "accumulatedDepreciation": [], // 类型：array，单位：万元，第N年累计折旧
      "fixedAssetsNetValue": [], // 类型：array，单位：万元，第N年固定资产净值
      "totalLiabilities": [], // 类型：array，单位：万元，第N年负债合计
      "currentLiabilities": [], // 类型：array，单位：万元，第N年流动负债
      "nonCurrentLiabilities": [], // 类型：array，单位：万元，第N年非流动负债
      "longTermLoan": [], // 类型：array，单位：万元，第N年长期借款
      "totalEquity": [], // 类型：array，单位：万元，第N年所有者权益合计
      "paidInCapital": [], // 类型：array，单位：万元，第N年实收资本
      "retainedEarnings": [], // 类型：array，单位：万元，第N年留存收益
      "assetLiabilityRatio": [] // 类型：array，单位：无量纲，第N年资产负债率
  },

  // ===== 13. 财务指标汇总表 =====
  "financialIndicatorsSummary": {
      // 基本信息
      "installedCapacity": 400, // 类型：number，单位：MW，装机规模
      "operatingYears": 25, // 类型：number，单位：年，项目经营期
      
      // 发电量指标
      "averageAnnualGeneration": 0, // 类型：number，单位：万kW·h，年均发电量
      "totalGridElectricity": 0, // 类型：number，单位：万kW·h，上网总结算电量
      "averageAnnualGridElectricity": 0, // 类型：number，单位：万kW·h，年均上网结算电量
      "annualEffectiveUtilizationHours": 0, // 类型：number，单位：小时，年有效利用小时数
      
      // 投资指标
      "projectTotalInvestment": 47413.***********, // 类型：number，单位：万元，项目总投资
      "fixedAssetsStaticInvestment": 46598.87752, // 类型：number，单位：万元，固定资产静态投资
      "constructionPeriodInterest": 514.*************, // 类型：number，单位：万元，建设期利息
      "workingCapital": 300, // 类型：number，单位：万元，流动资金
      "staticInvestmentPerWatt": 0, // 类型：number，单位：元/W，单瓦静态总投资
      "dynamicInvestmentPerWatt": 0, // 类型：number，单位：元/W，单瓦动态总投资
      "investmentCostPerKWh": 0, // 类型：number，单位：元/kWh，度电投资成本
      
      // 资金来源
      "equityCapitalAmount": 14223.***********, // 类型：number，单位：万元，项目资本金投入金额
      "totalBankLoanAmount": 33189.***********, // 类型：number，单位：万元，项目银行贷款总额
      
      // 经营指标
      "totalSalesRevenue": 0, // 类型：number，单位：万元，销售收入总额
      "totalCostExpense": 0, // 类型：number，单位：万元，总成本费用
      "averageAnnualOperatingCost": 0, // 类型：number，单位：万元，年平均运营成本
      "totalGenerationCostPerUnit": 0, // 类型：number，单位：元/kWh，单位发电总成本
      "operatingCostPerKWh": 0, // 类型：number，单位：元/kWh，度电运营成本
      "totalSalesTaxSurcharges": 0, // 类型：number，单位：万元，销售税金附加总额
      "totalPowerGenerationProfit": 0, // 类型：number，单位：万元，发电利润总额
      "levelizedCostOfElectricity": 0, // 类型：number，单位：元/kWh，平准化度电成本LCOE
      
      // 回收期指标
      "paybackPeriodAfterTax_static": 0, // 类型：number，单位：年，税后投资回收期(静态)
      "paybackPeriodAfterTax_dynamic": 0, // 类型：number，单位：年，税后投资回收期(动态)
      
      // 收益率指标
      "projectInvestmentFIRR_beforeTax": 0, // 类型：number，单位：%，项目投资财务内部收益率_税前
      "projectInvestmentFIRR_afterTax": 0, // 类型：number，单位：%，项目投资财务内部收益率_税后
      "equityCapitalFIRR_beforeTax": 0, // 类型：number，单位：%，资本金财务内部收益率_税前
      "equityCapitalFIRR_afterTax": 0, // 类型：number，单位：%，资本金财务内部收益率_税后
      
      // 其他财务指标
      "hydrogenPrice": 25, // 类型：number，单位：元/kg，氢气价格
      "projectInvestmentNPV_beforeTax": 0, // 类型：number，单位：万元，项目投资财务税前净现值
      "equityCapitalNPV_afterTax": 0, // 类型：number，单位：万元，资本金财务税后净现值
      "returnOnInvestment": 0, // 类型：number，单位：%，总投资收益率ROI
      "investmentTaxRate": 0, // 类型：number，单位：%，投资利税率
      "returnOnEquity": 0, // 类型：number，单位：%，项目资本金净利润率ROE
      "assetLiabilityRatio": 0, // 类型：number，单位：%，资产负债率
      "vatRefund50Percent": 0, // 类型：number，单位：万元，增值税即征即退50%
      "lcoe": 0, // 类型：number，单位：元/kWh，平准化度电成本
      "lcoh": 0, // 类型：number，单位：元/kg，平准化制氢成本
      "lcoh_ori": 0 // 类型：number，单位：元/kg，原始平准化制氢成本
  },
};

// 导出结果
module.exports = result;