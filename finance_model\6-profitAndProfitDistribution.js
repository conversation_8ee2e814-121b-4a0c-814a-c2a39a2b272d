/**
 * 计算利润和利润分配表
 * @param {Object} params - 输入参数
 * @param {boolean} showLogs - 是否显示表格数据（可选，默认为false）
 * @returns {Object} - 利润和利润分配表结果
 */
function calculateProfitAndProfitDistribution(params, showLogs = false) {
    const {
        operatingYears, // 项目运营年限
        hydrogenProduction, // 第N年制氢量
        hydrogenPriceNoTax, // 氢气售价不含税
        oxygenProduction, // 第N年制氧量
        oxygenPriceNoTax, // 氧气售价不含税
        gridElectricity, // 第N年上网电量
        gridElectricityPriceNoTax, // 脱硫煤电价不含税
        revenueItemVATRate, // 收入项增值税率
        cityConstructionAndMaintenanceTaxRate, // 城市建设维护税率
        educationSurchargeRate, // 教育费附加税率
        localEducationSurchargeRate, // 地方教育费附加税率
        totalEducationSurchargeRate, // 总教育费附加税率
        totalCostExpense, // 第N年总成本费用
        annualInterest, // 第N年本年付息
        depreciation, // 第N年折旧费
        incomeTaxRate, // 第N年所得税率
        statutorySurplusReserveFundRate, // 法定盈余公积金率
        shareholderDividendRatio, // 股东分红占比
        inputTax, // 第N年进项税
        projectTotalInvestment, // 项目总投资
        totalProjectInvestment, // 工程总投资 (新增参数)
        fixedAssetsNoTax // 固定资产不含税
    } = params;

    // 创建结果对象
    const years = operatingYears + 1; // +1 是因为包括第1年(建设期)
    const results = {
        nonTaxOperatingIncome: new Array(years).fill(0), // 第N年不含税营业收入
        outputTax: new Array(years).fill(0), // 第N年销项税
        inputTaxEndingBalance: new Array(years).fill(0), // 第N年进项税期末余额
        inputTaxBeginningBalance: new Array(years).fill(0), // 第N年进项税期初余额
        currentPeriodInputTax: new Array(years).fill(0), // 第N年本期进项税额
        inputTaxDeduction: new Array(years).fill(0), // 第N年进项税抵扣额
        valueAddedTax: new Array(years).fill(0), // 第N年应缴税金
        inputTaxRemainingAmount: new Array(years).fill(0), // 第N年进项税留底金额
        cityConstructionTax: new Array(years).fill(0), // 第N年城建税
        totalEducationSurcharge: new Array(years).fill(0), // 第N年总教育费附加
        localEducationSurcharge: new Array(years).fill(0), // 第N年地方教育费附加
        salesTaxAndAdditions: new Array(years).fill(0), // 第N年销售税金及附加
        taxableSubsidy: new Array(years).fill(0), // 第N年补贴收入应税
        taxExemptSubsidy: new Array(years).fill(0), // 第N年补贴收入免税
        totalProfit: new Array(years).fill(0), // 第N年利润总额
        accumulatedTotalProfit: new Array(years).fill(0), // 第N年累计利润总额
        previousYearLossCompensation: new Array(years).fill(0), // 第N年弥补以前年度亏损
        taxableIncome: new Array(years).fill(0), // 第N年应纳税所得额
        incomeTax: new Array(years).fill(0), // 第N年所得税
        netProfit: new Array(years).fill(0), // 第N年净利润
        initialUndistributedProfit: new Array(years).fill(0), // 第N年期初未分配的利润
        statutoryReserveFund: new Array(years).fill(0), // 第N年提取法定盈余公积金
        profitAvailableForDistribution: new Array(years).fill(0), // 第N年可供投资者分配的利润
        payableProfit: new Array(years).fill(0), // 第N年应付利润
        undistributedProfit: new Array(years).fill(0), // 第N年未分配利润
        interestAndTaxProfit: new Array(years).fill(0), // 第N年息税前利润
        EBITDA: new Array(years).fill(0) // 第N年息税折旧摊销前利润
    };

    // 计算各年数据
    for (let i = 0; i < years; i++) {
        // 第N年不含税营业收入 = 第N年制氢量 * 氢气售价不含税 + 第N年制氧量 * 氧气售价不含税 + 脱硫煤电价不含税 * 第N年上网电量
        results.nonTaxOperatingIncome[i] = 
            hydrogenProduction[i] * hydrogenPriceNoTax + 
            oxygenProduction[i] * oxygenPriceNoTax + 
            gridElectricityPriceNoTax * gridElectricity[i];
        
        // 第N年销项税
        if (i === 0) {
            results.outputTax[i] = 0; // 第1年销项税=0
        } else {
            // 第N(N>=2)年销项税 = 第N年不含税营业收入 * 收入项增值税率
            results.outputTax[i] = results.nonTaxOperatingIncome[i] * revenueItemVATRate;
        }
        
        // 第N年进项税期初余额
        if (i === 0) {
            results.inputTaxBeginningBalance[i] = 0; // 第1年进项税期初余额=0
        } else {
            // 第N(N>=2)年进项税期初余额 = 第N-1年进项税期末余额
            results.inputTaxBeginningBalance[i] = results.inputTaxEndingBalance[i-1];
        }
        
        // 第N年本期进项税额
        if (i === 0) {
            // 第1年本期进项税额 = 第1年进项税期末余额
            results.currentPeriodInputTax[i] = totalProjectInvestment - fixedAssetsNoTax;
        } else {
            // 第N年本期进项税额 = 第N年进项税
            results.currentPeriodInputTax[i] = inputTax[i];
        }
        
        // 第N年进项税期末余额
        if (i === 0) {
            // 第1年进项税期末余额 = 工程总投资 - 固定资产不含税
            results.inputTaxEndingBalance[i] = totalProjectInvestment - fixedAssetsNoTax;
        } else {
            // 第N年进项税期末余额 = 如果(第N年进项税期初余额 + 第N年本期进项税额 > 第N年销项税，则返回第N年进项税期初余额 + 第N年本期进项税额 - 第N年销项税，否则返回0)
            const totalInputTax = results.inputTaxBeginningBalance[i] + results.currentPeriodInputTax[i];
            results.inputTaxEndingBalance[i] = totalInputTax > results.outputTax[i] ? 
                                          totalInputTax - results.outputTax[i] : 0;
        }
        
        // 第N年进项税抵扣额
        if (i === 0) {
            results.inputTaxDeduction[i] = 0; // 第1年进项税抵扣额=0
        } else {
            // 第N年进项税抵扣额 = 第N年进项税期初余额 + 第N年本期进项税额 - 第N年进项税期末余额
            results.inputTaxDeduction[i] = results.inputTaxBeginningBalance[i] + 
                                      results.currentPeriodInputTax[i] - 
                                      results.inputTaxEndingBalance[i];
        }
        
        // 第N年应缴税金
        if (i === 0) {
            results.valueAddedTax[i] = 0; // 第1年应缴税金=0
        } else {
            // 第N年应缴税金 = 第N年销项税 - 第N年进项税抵扣额
            results.valueAddedTax[i] = results.outputTax[i] - results.inputTaxDeduction[i];
        }
        
        // 第N年进项税留底金额
        if (i === 0) {
            results.inputTaxRemainingAmount[i] = 0; // 第1年进项税留底金额=0
        } else {
            // 第N年进项税留底金额 = 第N年进项税期初余额 + 第N年本期进项税额 - 第N年销项税
            const remainingAmount = results.inputTaxBeginningBalance[i] + 
                                 results.currentPeriodInputTax[i] - 
                                 results.outputTax[i];
            results.inputTaxRemainingAmount[i] = remainingAmount;
        }
        
        // 第N年城建税
        if (i === 0) {
            results.cityConstructionTax[i] = 0; // 第1年城建税=0
        } else {
            // 第N年城建税 = 第N年应缴税金 * 城市建设维护税率
            results.cityConstructionTax[i] = results.valueAddedTax[i] * cityConstructionAndMaintenanceTaxRate;
        }
        
        // 第N年总教育费附加
        if (i === 0) {
            results.totalEducationSurcharge[i] = 0; // 第1年教育费附加=0
        } else {
            // 第N年总教育费附加 = 第N年应缴税金 * 总教育费附加税率
            results.totalEducationSurcharge[i] = results.valueAddedTax[i] * totalEducationSurchargeRate;
        }
        
        // 第N年地方教育费附加
        if (i === 0) {
            results.localEducationSurcharge[i] = 0; // 第1年地方教育费附加=0
        } else {
            // 第N年地方教育费附加 = 第N年应缴税金 * 地方教育费附加税率
            results.localEducationSurcharge[i] = results.valueAddedTax[i] * localEducationSurchargeRate;
        }
        
        // 第N年销售税金及附加
        if (i === 0) {
            results.salesTaxAndAdditions[i] = 0; // 第1年销售税金及附加=0
        } else {
            // 第N年销售税金及附加 = 第N年总教育费附加 + 第N年城建税
            results.salesTaxAndAdditions[i] = results.totalEducationSurcharge[i] + results.cityConstructionTax[i];
        }
        
        // 第N年补贴收入应税
        results.taxableSubsidy[i] = 0; // 默认值为0
        
        // 第N年补贴收入免税
        results.taxExemptSubsidy[i] = 0; // 默认值为0
        
        // 第N年利润总额
        if (i === 0) {
            results.totalProfit[i] = 0; // 第1年利润总和=0
        } else {
            // 第N年利润总和 = 第N年不含税营业收入 - 第N年销售税金及附加 - 第N年总成本费用 + 第N年补贴收入应税
            results.totalProfit[i] = results.nonTaxOperatingIncome[i] - 
                                 results.salesTaxAndAdditions[i] - 
                                 totalCostExpense[i] + 
                                 results.taxableSubsidy[i];
        }
        
        // 第N年累计利润总额
        if (i === 0) {
            results.accumulatedTotalProfit[i] = 0; // 第1年累计利润总额=0
        } else {
            // 第N年累计利润总额 = 第N年利润总额 + 第N-1年累计利润总额
            results.accumulatedTotalProfit[i] = results.totalProfit[i] + results.accumulatedTotalProfit[i-1];
        }
        
        // 第N年弥补以前年度亏损
        if (i === 0) {
            results.previousYearLossCompensation[i] = 0; // 第1年弥补以前年度亏损=0
        } else {
            // 第N年弥补以前年度亏损 = 
            // 如果(第N年利润总额 < 0，则返回0，
            // 否则(如果(第N-1年累计利润总额 >= 0，则返回0，
            // 否则(如果(第N年利润总额 + 第N-1年累计利润总额 > 0，则返回-第N-1年累计利润总额，
            // 否则返回第N年利润总额))))
            if (results.totalProfit[i] <= 0) {
                results.previousYearLossCompensation[i] = 0;
            } else {
                if (results.accumulatedTotalProfit[i-1] >= 0) {
                    results.previousYearLossCompensation[i] = 0;
                } else {
                    if (results.totalProfit[i] + results.accumulatedTotalProfit[i-1] > 0) {
                        results.previousYearLossCompensation[i] = -results.accumulatedTotalProfit[i-1];
                    } else {
                        results.previousYearLossCompensation[i] = results.totalProfit[i];
                    }
                }
            }
        }
        
        // 第N年应纳税所得额
        // 第N年应纳税所得额 = 如果((第N年利润总额 - 第N年弥补以前年度亏损) <= 0，则返回0，否则返回第N年利润总额 - 第N年弥补以前年度亏损)
        const taxableIncomeCalc = results.totalProfit[i] - results.previousYearLossCompensation[i];
        results.taxableIncome[i] = taxableIncomeCalc <= 0 ? 0 : taxableIncomeCalc;
        
        // 第N年所得税 = 第N年应纳税所得额 * 第N年所得税率
        results.incomeTax[i] = results.taxableIncome[i] * incomeTaxRate[i];
        
        // 第N年净利润
        if (i === 0) {
            results.netProfit[i] = 0; // 第1年净利润=0
        } else if (i === 1) {
            // 第2年净利润 = 第N年利润总额 - 第N年弥补以前年度亏损 - 第N年所得税 + 第N年补贴收入免税
            results.netProfit[i] = results.totalProfit[i] - 
                               results.previousYearLossCompensation[i] - 
                               results.incomeTax[i] + 
                               results.taxExemptSubsidy[i];
        } else {
            // 第N年净利润 = 第N年利润总额 - 第N年所得税 + 第N年补贴收入免税
            results.netProfit[i] = results.totalProfit[i] - 
                               results.incomeTax[i] + 
                               results.taxExemptSubsidy[i];
        }
        
        // 第N年期初未分配的利润 = 第N-1年未分配利润
        if (i === 0) {
            results.initialUndistributedProfit[i] = 0; // 第1年期初未分配的利润=0
        } else {
            results.initialUndistributedProfit[i] = results.undistributedProfit[i-1];
        }
        
        // 第N年提取法定盈余公积金
        if (i === 0) {
            results.statutoryReserveFund[i] = 0; // 第1年提取法定盈余公积金=0
        } else {
            // 第N年提取法定盈余公积金 = 如果(第N年期初未分配的利润 < 0，则返回0，否则(如果(第N年净利润 <= 0，则返回0，否则返回第N年净利润 * 法定盈余公积金率)))
            if (results.initialUndistributedProfit[i] < 0) {
                results.statutoryReserveFund[i] = 0;
            } else {
                if (results.netProfit[i] <= 0) {
                    results.statutoryReserveFund[i] = 0;
                } else {
                    results.statutoryReserveFund[i] = results.netProfit[i] * statutorySurplusReserveFundRate;
                }
            }
        }
        
        // 第N年可供投资者分配的利润
        if (i === 0) {
            results.profitAvailableForDistribution[i] = 0; // 第1年可供投资者分配的利润=0
        } else {
            // 第N年可供投资者分配的利润 = 第N年净利润 + 第N年期初未分配的利润 - 第N年提取法定盈余公积金
            results.profitAvailableForDistribution[i] = results.netProfit[i] + 
                                                   results.initialUndistributedProfit[i] - 
                                                   results.statutoryReserveFund[i];
        }
        
        // 第N年应付利润
        if (i === 0) {
            results.payableProfit[i] = 0; // 第1年应付利润=0
        } else {
            // 第N年应付利润 = 如果(第N年可供投资者分配的利润 - 第N年期初未分配的利润 <= 0，则返回0，否则返回(第N年可供投资者分配的利润 - 第N年期初未分配的利润) * 股东分红占比)
            const distributableProfit = results.profitAvailableForDistribution[i] - results.initialUndistributedProfit[i];
            results.payableProfit[i] = distributableProfit <= 0 ? 0 : distributableProfit * shareholderDividendRatio;
        }
        
        // 第N年未分配利润
        if (i === 0) {
            results.undistributedProfit[i] = 0; // 第1年未分配利润=0
        } else {
            // 第N年未分配利润 = 第N年期初未分配的利润 + 第N年净利润 - 第N年提取法定盈余公积金 - 第N年应付利润
            results.undistributedProfit[i] = results.initialUndistributedProfit[i] + 
                                        results.netProfit[i] - 
                                        results.statutoryReserveFund[i] - 
                                        results.payableProfit[i];
        }
        
        // 第N年息税前利润
        if (i === 0) {
            results.interestAndTaxProfit[i] = 0; // 第1年息税前利润=0
        } else {
            // 第N年息税前利润 = 第N年利润总额 + 第N年本年付息
            results.interestAndTaxProfit[i] = results.totalProfit[i] + annualInterest[i];
        }
        
        // 第N年息税折旧摊销前利润
        if (i === 0) {
            results.EBITDA[i] = 0; // 第1年息税折旧摊销前利润=0
        } else {
            // 第N年息税折旧摊销前利润 = 第N年息税前利润 + 第N年折旧费
            results.EBITDA[i] = results.interestAndTaxProfit[i] + depreciation[i];
        }
    }

    // 为了更好的可读性，将输出分为多个表格
    if (showLogs) {
        console.log("\n===== 利润和利润分配表 =====");
        
        // 1. 显示收入相关数据
        const incomeResults = {};
        for (let i = 0; i < years; i++) {
            incomeResults[`第${i}年`] = {
                "不含税营业收入": results.nonTaxOperatingIncome[i],
                "销项税": results.outputTax[i],
                "补贴收入应税": results.taxableSubsidy[i],
                "补贴收入免税": results.taxExemptSubsidy[i]
            };
        }
        console.log("1. 收入情况");
        console.table(incomeResults);
        
        // 2. 显示进项税相关数据
        const inputTaxResults = {};
        for (let i = 0; i < years; i++) {
            inputTaxResults[`第${i}年`] = {
                "进项税期初余额": results.inputTaxBeginningBalance[i],
                "本期进项税额": results.currentPeriodInputTax[i],
                "进项税期末余额": results.inputTaxEndingBalance[i],
                "进项税抵扣额": results.inputTaxDeduction[i],
                "进项税留底金额": results.inputTaxRemainingAmount[i]
            };
        }
        console.log("\n2. 进项税情况");
        console.table(inputTaxResults);
        
        // 3. 显示税费相关数据
        const taxResults = {};
        for (let i = 0; i < years; i++) {
            taxResults[`第${i}年`] = {
                "应缴税金": results.valueAddedTax[i],
                "城建税": results.cityConstructionTax[i],
                "总教育费附加": results.totalEducationSurcharge[i],
                "地方教育费附加": results.localEducationSurcharge[i],
                "销售税金及附加": results.salesTaxAndAdditions[i]
            };
        }
        console.log("\n3. 税费情况");
        console.table(taxResults);
        
        // 4. 显示利润相关数据
        const profitResults = {};
        for (let i = 0; i < years; i++) {
            profitResults[`第${i}年`] = {
                "利润总额": results.totalProfit[i],
                "累计利润总额": results.accumulatedTotalProfit[i],
                "弥补以前年度亏损": results.previousYearLossCompensation[i],
                "应纳税所得额": results.taxableIncome[i],
                "所得税": results.incomeTax[i],
                "净利润": results.netProfit[i]
            };
        }
        console.log("\n4. 利润情况");
        console.table(profitResults);
        
        // 5. 显示利润分配相关数据
        const profitDistributionResults = {};
        for (let i = 0; i < years; i++) {
            profitDistributionResults[`第${i}年`] = {
                "期初未分配的利润": results.initialUndistributedProfit[i],
                "提取法定盈余公积金": results.statutoryReserveFund[i],
                "可供投资者分配的利润": results.profitAvailableForDistribution[i],
                "应付利润": results.payableProfit[i],
                "未分配利润": results.undistributedProfit[i]
            };
        }
        console.log("\n5. 利润分配情况");
        console.table(profitDistributionResults);
        
        // 6. 显示其他关键财务指标
        const otherFinancialIndicatorsResults = {};
        for (let i = 0; i < years; i++) {
            otherFinancialIndicatorsResults[`第${i}年`] = {
                "息税前利润": results.interestAndTaxProfit[i],
                "息税折旧摊销前利润": results.EBITDA[i]
            };
        }
        console.log("\n6. 其他关键财务指标");
        console.table(otherFinancialIndicatorsResults);
    }

    return results;
}

module.exports = { calculateProfitAndProfitDistribution }; 