const inputs = require('./inputs');
const { calculateKeyIndicators } = require('./0-keyIndicators');
const { calculateFixedAssetsInvestmentEstimation } = require('./3-fixedAssetsInvestmentEstimation');
const { calculateProjectOverallBudget } = require('./1-projectOverallBudget');
const { calculateFinancingPlan } = require('./2-financingPlan');
const { calculateInvestmentPlanAndFundRaising } = require('./4-investmentPlanAndFundRaising');
const { calculateAnnualHydrogenAndGridPower } = require('./13-annualHydrogenAndGridPower');
const { calculateLoanRepaymentSchedule } = require('./7-loanRepaymentSchedule');
const { calculateTotalCostAndExpenses } = require('./5-totalCostAndExpenses');
const { calculateProfitAndProfitDistribution } = require('./6-profitAndProfitDistribution');
const { calculateProjectInvestmentCashFlow } = require('./8-projectInvestmentCashFlow');
const { calculateEquityCapitalCashFlow } = require('./9-equityCapitalCashFlow');
const { calculateFinancialPlanCashFlow } = require('./10-financialPlanCashFlow');
const { calculateBalanceSheet } = require('./11-balanceSheet');
const { calculateFinancialIndicatorsSummary } = require('./12-financialIndicatorsSummary');

/**
 * 执行完整的财务测算模型计算
 * @param {Object} customInputs - 自定义输入参数（可选）
 * @param {boolean} showLogs - 是否显示表格数据（可选，默认为false）
 * @returns {Object} - 所有模块的计算结果
 */
function runFinancialModel(customInputs = {}, showLogs = false) {
    // 合并自定义输入参数与默认参数
    const mergedInputs = { ...inputs, ...customInputs };
    const results = {};
    
    // 1. 计算关键指标
    results.keyIndicators = calculateKeyIndicators(mergedInputs, showLogs);
    
    // 2. 计算固定资产投资估算
    results.fixedAssetsInvestmentEstimation = calculateFixedAssetsInvestmentEstimation({
        ...mergedInputs,
        ...results.keyIndicators
    }, showLogs);
    
    // 3. 计算工程总概算
    results.projectOverallBudget = calculateProjectOverallBudget({
        ...mergedInputs,
        ...results.fixedAssetsInvestmentEstimation
    }, showLogs);
    
    // 4. 计算融资计划
    results.financingPlan = calculateFinancingPlan({
        ...mergedInputs,
        ...results.fixedAssetsInvestmentEstimation
    }, showLogs);
    
    // 5. 计算投资计划与资金筹措
    results.investmentPlanAndFundRaising = calculateInvestmentPlanAndFundRaising({
        ...mergedInputs,
        ...results.keyIndicators,
        ...results.fixedAssetsInvestmentEstimation,
        ...results.financingPlan
    }, showLogs);
    
    // 6. 计算年度制氢及上网电量
    results.annualHydrogenAndGridPower = calculateAnnualHydrogenAndGridPower(mergedInputs, showLogs);
    
    // 7. 计算还本付息计划
    results.loanRepaymentSchedule = calculateLoanRepaymentSchedule({
        ...mergedInputs,
        ...results.keyIndicators,
        ...results.investmentPlanAndFundRaising
    }, showLogs);
    
    // 8. 计算总成本费用
    results.totalCostAndExpenses = calculateTotalCostAndExpenses({
        ...mergedInputs,
        ...results.keyIndicators,
        ...results.fixedAssetsInvestmentEstimation,
        ...results.annualHydrogenAndGridPower,
        ...results.loanRepaymentSchedule
    }, showLogs);
    
    // 9. 计算利润和利润分配
    results.profitAndProfitDistribution = calculateProfitAndProfitDistribution({
        ...mergedInputs,
        ...results.keyIndicators,
        ...results.annualHydrogenAndGridPower,
        ...results.fixedAssetsInvestmentEstimation,
        ...results.totalCostAndExpenses,
        ...results.loanRepaymentSchedule,
        ...results.projectOverallBudget
    }, showLogs);
    
    // 10. 计算项目投资现金流量
    results.projectInvestmentCashFlow = calculateProjectInvestmentCashFlow({
        ...mergedInputs,
        ...results.keyIndicators,
        ...results.annualHydrogenAndGridPower,
        ...results.fixedAssetsInvestmentEstimation,
        ...results.totalCostAndExpenses,
        ...results.profitAndProfitDistribution,
        ...results.loanRepaymentSchedule
    }, showLogs);
    
    // 11. 计算资本金财务现金流量
    results.equityCapitalCashFlow = calculateEquityCapitalCashFlow({
        ...mergedInputs,
        ...results.financingPlan,
        ...results.fixedAssetsInvestmentEstimation,
        ...results.profitAndProfitDistribution,
        ...results.totalCostAndExpenses,
        ...results.loanRepaymentSchedule,
        ...results.projectInvestmentCashFlow
    }, showLogs);
    
    // 12. 计算财务计划现金流量
    results.financialPlanCashFlow = calculateFinancialPlanCashFlow({
        ...mergedInputs,
        ...results.financingPlan,
        ...results.investmentPlanAndFundRaising,
        ...results.fixedAssetsInvestmentEstimation,
        ...results.profitAndProfitDistribution,
        ...results.totalCostAndExpenses,
        ...results.loanRepaymentSchedule,
        ...results.projectInvestmentCashFlow,
        ...results.equityCapitalCashFlow
    }, showLogs);
    
    // 13. 计算资产负债表
    results.balanceSheet = calculateBalanceSheet({
        ...mergedInputs,
        ...results.financingPlan,
        ...results.fixedAssetsInvestmentEstimation,
        ...results.projectInvestmentCashFlow,
        ...results.financialPlanCashFlow,
        ...results.totalCostAndExpenses,
        ...results.profitAndProfitDistribution,
        ...results.loanRepaymentSchedule
    }, showLogs);
    
    // 14. 计算财务指标汇总
    results.financialIndicatorsSummary = calculateFinancialIndicatorsSummary({
        ...mergedInputs,
        ...results.keyIndicators,
        ...results.financingPlan,
        ...results.fixedAssetsInvestmentEstimation,
        ...results.profitAndProfitDistribution,
        ...results.projectInvestmentCashFlow,
        ...results.equityCapitalCashFlow,
        ...results.totalCostAndExpenses,
        ...results.balanceSheet,
        ...results.annualHydrogenAndGridPower
    }, showLogs);
    
    return results;
}

module.exports = { runFinancialModel };