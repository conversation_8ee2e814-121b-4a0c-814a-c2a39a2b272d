/**
 * 计算年度制氢及上网电量
 * @param {Object} params - 输入参数
 * @param {boolean} showLogs - 是否显示表格数据（可选，默认为false）
 * @returns {Object} - 年度制氢及上网电量结果
 */
function calculateAnnualHydrogenAndGridPower(params, showLogs = false) {
    const {
        installedCapacity, // 装机量
        pvCapacity, // 光伏装机量
        windCapacity, // 风机装机量
        pvFirstYearPowerGenerationHour, // 光伏首年发电小时数
        windFirstYearPowerGenerationHour, // 风机首年发电小时数
        operatingYears, // 项目运营年限
        yearTwoDegradationPercentage, // 第2年衰减百分值
        yearNDegradationPercentage, // 第N年衰减百分值
        powerToHydrogenRatio, // 发电制氢比例
        discountRate, // 折现系数
        powerGenerationCostSensitivityFactor, // 发电成本敏感系数
        electrolyzerPowerConsumption, // 电解槽电耗

        firstYearDownGridPower, // 首年下网电量
        genH2ConsumeWater, // 制氢耗水量(L/Nm³)
        isFromCapacity,
        capfirstYearGreenPower,
        capfirstYearHydrogenElectricity,
        capfirstYearUpGridPower,
        capfirstYearHydrogenProduction,
        capfirstYearGridHydrogenProduction
    } = params;

    // 创建结果对象，包括所有年份的数据
    const years = operatingYears + 1; // +1 是因为包括第1年(建设期)
    const results = {
        yearDegradationPercentage: new Array(years).fill(0), // 第N年衰减百分值
        pvFirstYearPowerGenerationHour: pvFirstYearPowerGenerationHour, // 光伏首年发电小时数
        windFirstYearPowerGenerationHour: windFirstYearPowerGenerationHour, // 风机首年发电小时数
        componentDegradationEfficiency: new Array(years).fill(0), // 第N年组件衰减效率
        powerLimitationRate: new Array(years).fill(0), // 第N年限电率
        powerGenerationRate: new Array(years).fill(0), // 第N年发电率
        predictedPowerGenerationAfterDegradation: new Array(years).fill(0), // 第N年预计发电量衰减后(单位：万度)
        predictedPowerGenerationWithPowerLimitation: new Array(years).fill(0), // 第N年预计发电量衰减加限电(单位：万度)
        gridElectricity: new Array(years).fill(0), // 第N年上网电量
        greenHydrogenElectricity: new Array(years).fill(0), // 第N年绿电制氢电量
        powerDiscountFactor: new Array(years).fill(0), // 第N年发电折现系数
        discountedPowerGeneration: new Array(years).fill(0), // 第N年发电量折现值
        gridHydrogenElectricity: new Array(years).fill(0), // 第N年下网制氢电量
        hydrogenProduction: new Array(years).fill(0), // 第N年制氢量(万公斤)
        oxygenProduction: new Array(years).fill(0), // 第N年制氧量(万公斤)
        waterConsumption: new Array(years).fill(0), // 第N年耗水量(万吨)
        greenPowerRatioAfterYears: new Array(years).fill(0)
    };

    // 计算运行首年发电量(单位：万度) = 首年发电小时数 * 装机量 / 10
    const firstYearGeneratedPower = pvFirstYearPowerGenerationHour * pvCapacity / 10 + windFirstYearPowerGenerationHour * windCapacity / 10;
    
    // 计算各年数据
    for (let i = 0; i < years; i++) {
        // 第N年衰减百分值
        if (i === 0) {
            results.yearDegradationPercentage[i] = 0; // 第1年衰减百分值=0%
        } else if (i === 1) {
            results.yearDegradationPercentage[i] = yearTwoDegradationPercentage; // 第2年衰减百分值
        } else {
            results.yearDegradationPercentage[i] = yearNDegradationPercentage; // 第N年衰减百分值
        }
        
        // 第N年组件衰减效率
        if (i === 0) {
            results.componentDegradationEfficiency[i] = 0; // 第1年组件衰减效率=0%
        } else if (i === 1) {
            results.componentDegradationEfficiency[i] = 1 - results.yearDegradationPercentage[i]; // 第2年组件衰减效率=1-第2年衰减百分值
        } else {
            // 第N年组件衰减效率=第2年组件衰减效率-(N-2)*第N年衰减百分值
            results.componentDegradationEfficiency[i] = results.componentDegradationEfficiency[1] - (i - 1) * results.yearDegradationPercentage[i];
        }
        
        // 第N年限电率
        if (i === 0) {
            results.powerLimitationRate[i] = 0; // 第1年限电率=0%
        } else {
            results.powerLimitationRate[i] = 0; // 第N年限电率默认为0%
        }
        
        // 第N年发电率
        if (i === 0) {
            results.powerGenerationRate[i] = 0; // 第1年发电率=0
        } else {
            // 第N年发电率=1-第N年限电率
            results.powerGenerationRate[i] = 1 - results.powerLimitationRate[i];
        }

        // TODO: 第N年绿电衰减后可用比例
        if (i === 0) {
            results.greenPowerRatioAfterYears[i] = 0;
        } else if (i === 1) {
            results.greenPowerRatioAfterYears[i] = 1;
        } else {
            // 第N年=1/(1-(第N年衰减百分值/2))*(1-(第2年组件衰减效率-第N年组件衰减效率))
            results.greenPowerRatioAfterYears[i] = 1 / 
                (1 - (results.yearDegradationPercentage[i] / 2)) * 
                (1 - (results.componentDegradationEfficiency[1] - results.componentDegradationEfficiency[i]));
        }
        
        
        
        // TODO:第N年预计发电量衰减后(单位：万度)
        if (isFromCapacity) {
            results.predictedPowerGenerationAfterDegradation[i] = results.greenPowerRatioAfterYears[i] * capfirstYearGreenPower
        } else {
            if (i === 0) {
                results.predictedPowerGenerationAfterDegradation[i] = 0; // 第1年预计发电量衰减后=0
            } else if (i === 1) {
                // 第2年预计发电量衰减后=运行首年发电量
                results.predictedPowerGenerationAfterDegradation[i] = firstYearGeneratedPower;
            } else {
                // 第N年预计发电量衰减后=运行首年发电量/(1-(第N年衰减百分值/2))*(1-(第2年组件衰减效率-第N年组件衰减效率))
                results.predictedPowerGenerationAfterDegradation[i] = firstYearGeneratedPower / 
                    (1 - (results.yearDegradationPercentage[i] / 2)) * 
                    (1 - (results.componentDegradationEfficiency[1] - results.componentDegradationEfficiency[i]));
            }
        }

        
        // 第N年预计发电量衰减加限电(单位：万度)
        if (i === 0) {
            results.predictedPowerGenerationWithPowerLimitation[i] = 0; // 第1年预计发电量衰减加限电=0
        } else {
            // 第N年预计发电量衰减加限电=第N年发电率*第N年预计发电量衰减后*发电成本敏感系数
            results.predictedPowerGenerationWithPowerLimitation[i] = results.powerGenerationRate[i] * 
                results.predictedPowerGenerationAfterDegradation[i] * 
                powerGenerationCostSensitivityFactor;
        }
        
        // TODO:第N年上网电量
        if (isFromCapacity) {
            results.gridElectricity[i] = results.greenPowerRatioAfterYears[i] * capfirstYearUpGridPower
        } else {
            if (i === 0) {
                results.gridElectricity[i] = 0; // 第1年上网电量=0
            } else {
                // 第N年上网电量=第N年预计发电量衰减后*(1-发电制氢比例)
                results.gridElectricity[i] = results.predictedPowerGenerationAfterDegradation[i] * (1 - powerToHydrogenRatio);
            }
        }
       
        
        // TODO:第N年绿电制氢电量
        if (isFromCapacity) {
            results.greenHydrogenElectricity[i] = results.greenPowerRatioAfterYears[i] * capfirstYearHydrogenElectricity
        } else {
            if (i === 0) {
                results.greenHydrogenElectricity[i] = 0; // 第1年制氢电量=0
            } else {
                // 第N年绿电制氢电量=第N年预计发电量衰减加限电-第N年上网电量
                results.greenHydrogenElectricity[i] = results.predictedPowerGenerationWithPowerLimitation[i] - results.gridElectricity[i];
            }
        }
        
        
        // 第N年发电折现系数
        if (i === 0) {
            results.powerDiscountFactor[i] = 0; // 第1年发电折现系数=0
        } else {
            // 第N年发电折现系数=1/(1+折现系数)^(N+1)
            results.powerDiscountFactor[i] = 1 / Math.pow(1 + discountRate, i + 1);
        }
        
        // 第N年发电量折现值
        if (i === 0) {
            results.discountedPowerGeneration[i] = 0; // 第1年发电量折现值=0
        } else {
            // 第N年发电量折现值=第N年预计发电量衰减加限电*第N年发电折现系数
            results.discountedPowerGeneration[i] = results.predictedPowerGenerationWithPowerLimitation[i] * results.powerDiscountFactor[i];
        }
        
        // TODO:第N年下网制氢电量
        if (isFromCapacity) {
            if (i === 0) {
                results.gridHydrogenElectricity[i] = 0
            } else {
                results.gridHydrogenElectricity[i] = firstYearDownGridPower
            }
            
        } else {
            if (i === 0) {
                results.gridHydrogenElectricity[i] = 0; // 第1年下网制氢电量=0
            } else {
                results.gridHydrogenElectricity[i] = firstYearDownGridPower; // 默认值为0
            }
        }
        
        
        // TODO:第N年制氢量(万公斤)
        if (isFromCapacity) {
            if (i === 0) {
                results.hydrogenProduction[i] = 0
            } else {
                results.hydrogenProduction[i] = results.greenPowerRatioAfterYears[i] * capfirstYearHydrogenProduction + capfirstYearGridHydrogenProduction
            }
        } else {
            if (i === 0) {
                results.hydrogenProduction[i] = 0; // 第1年制氢量=0
            } else {
                // 电耗 = 电解槽电耗 / 0.0899
                const powerConsumption = electrolyzerPowerConsumption / 0.0899;
                // 第N年制氢量 = 第N年绿电制氢电量 / 电耗 + 第N年下网电制氢量 / 电耗
                results.hydrogenProduction[i] = results.greenHydrogenElectricity[i] / powerConsumption + 
                                            results.gridHydrogenElectricity[i] / powerConsumption;
            }
        }
       
        
        // 第N年制氧量(万公斤)
        if (i === 0) {
            results.oxygenProduction[i] = 0; // 第1年制氧量=0
        } else {
            results.oxygenProduction[i] = 0; // 默认值为0
        }
        
        // 第N年耗水量(万吨)
        if (i === 0) {
            results.waterConsumption[i] = 0; // 第1年耗水量=0
        } else {
            // 第N年耗水量=第N年制氢量*11.2*2/1000
            results.waterConsumption[i] = results.hydrogenProduction[i] * 11.2 * genH2ConsumeWater / 1000;
        }
    }

    // 只有在showLogs为true时才打印表格
    if (showLogs) {
        // 为了更好的可读性，分别显示各个主要计算结果的表格
        console.log("\n===== 年度制氢及上网电量 =====");
        
        // 显示关键发电参数
        const powerGenerationResults = {};
        for (let i = 0; i < years; i++) {
            powerGenerationResults[`第${i}年`] = {
                "衰减百分值": results.yearDegradationPercentage[i],
                "组件衰减效率": results.componentDegradationEfficiency[i],
                "第N年绿电衰减后可用比例": results.greenPowerRatioAfterYears[i],
                "限电率": results.powerLimitationRate[i],
                "发电率": results.powerGenerationRate[i],
                "预计发电量(衰减后)(万度)": results.predictedPowerGenerationAfterDegradation[i],
                "预计发电量(含限电)(万度)": results.predictedPowerGenerationWithPowerLimitation[i]
            };
        }
        console.log("1. 发电参数");
        console.table(powerGenerationResults);
        
        // 显示电力分配情况
        const powerDistributionResults = {};
        for (let i = 0; i < years; i++) {
            powerDistributionResults[`第${i}年`] = {
                "上网电量(万度)": results.gridElectricity[i],
                "绿电制氢电量(万度)": results.greenHydrogenElectricity[i],
                "下网制氢电量(万度)": results.gridHydrogenElectricity[i]
            };
        }
        console.log("\n2. 电力分配");
        console.table(powerDistributionResults);
        
        // 显示折现相关数据
        const discountResults = {};
        for (let i = 0; i < years; i++) {
            discountResults[`第${i}年`] = {
                "发电折现系数": results.powerDiscountFactor[i],
                "发电量折现值(万度)": results.discountedPowerGeneration[i]
            };
        }
        console.log("\n3. 折现计算");
        console.table(discountResults);
        
        // 显示制氢制氧与耗水量
        const hydrogenOxygenWaterResults = {};
        for (let i = 0; i < years; i++) {
            hydrogenOxygenWaterResults[`第${i}年`] = {
                "制氢量(万公斤)": results.hydrogenProduction[i],
                "制氧量(万公斤)": results.oxygenProduction[i],
                "耗水量(万吨)": results.waterConsumption[i]
            };
        }
        console.log("\n4. 制氢及耗水量");
        console.table(hydrogenOxygenWaterResults);

        // 显示首年发电量
        console.log(`\n5. 运行首年发电量(万度): ${firstYearGeneratedPower}`);
    }

    return results;
}

module.exports = { calculateAnnualHydrogenAndGridPower }; 