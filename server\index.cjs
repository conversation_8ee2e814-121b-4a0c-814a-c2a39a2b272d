const express = require('express');
const cors = require('cors');
const path = require('path');

// 引入财务计算模块
const distPath = path.resolve(__dirname, '../dist/run.cjs');
const { getFinanceData } = require(distPath);

const app = express();
const port = process.env.PORT || 9033;

// 中间件
app.use(cors());
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// 日志中间件
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

/**
 * 验证输入参数
 */
function validateInputs(inputs) {
  if (!inputs) {
    return {
      isValid: false,
      message: '输入参数不能为空'
    };
  }

  // 基本参数验证
  const requiredFields = ['config', 'baseLoanRate', 'operatingYears'];

  for (const field of requiredFields) {
    if (inputs[field] === undefined || inputs[field] === null) {
      return {
        isValid: false,
        message: `缺少必需参数: ${field}`
      };
    }
  }

  // 验证config.epcInfo
  if (!inputs.config || !inputs.config.epcInfo) {
    return {
      isValid: false,
      message: '缺少EPC配置信息'
    };
  }

  return {
    isValid: true,
    message: '参数验证通过'
  };
}

/**
 * 财务测算计算接口
 * POST /api/finance/calculate
 */
app.post('/api/finance/calculate', async (req, res) => {
  try {
    
    const { inputs, showLogs = false } = req.body;

    // 验证输入参数
    const validation = validateInputs(inputs);
    if (!validation.isValid) {
      return res.status(400).json({
        success: false,
        message: validation.message,
        statusCode: 400
      });
    }

    // 执行财务计算
    const result = getFinanceData(inputs, showLogs);

    res.json({
      success: true,
      data: result,
      message: '计算成功',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('财务测算计算错误:', error);
    
    res.status(500).json({
      success: false,
      message: `计算失败: ${error.message}`,
      error: error.stack,
      statusCode: 500,
      timestamp: new Date().toISOString()
    });
  }
});

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: '接口不存在',
    statusCode: 404,
    path: req.originalUrl,
    timestamp: new Date().toISOString()
  });
});

// 错误处理中间件
app.use((error, req, res, next) => {
  console.error('服务器错误:', error);
  res.status(500).json({
    success: false,
    message: '服务器内部错误',
    error: error.message,
    statusCode: 500,
    timestamp: new Date().toISOString()
  });
});

// 启动服务器
app.listen(port, () => {
  console.log('='.repeat(50));
  console.log(`财务测算API服务器启动成功！`);
  console.log(`服务地址: http://localhost:${port}`);
  console.log(`API文档: http://localhost:${port}/api/finance/info`);
  console.log(`健康检查: http://localhost:${port}/api/finance/health`);
  console.log(`计算接口: POST http://localhost:${port}/api/finance/calculate`);
  console.log('='.repeat(50));
});

module.exports = app;
