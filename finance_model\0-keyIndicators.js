/**
 * 计算关键指标
 * @param {Object} inputs - 输入参数
 * @param {boolean} showLogs - 是否显示表格数据（可选，默认为false）
 * @returns {Object} - 关键指标计算结果
 */
function calculateKeyIndicators(inputs, showLogs = false) {
    // 计算借款利率 = 基础借款利率 * 利率变化系数
    const loanRate = inputs.baseLoanRate * inputs.rateChangeCoefficient;
    
    // 计算总教育费附加税率 = 教育费附加税率 + 地方教育费附加税率
    const totalEducationSurchargeRate = inputs.educationSurchargeRate + inputs.localEducationSurchargeRate;

    
    
    // 只有在showLogs为true时才打印表格
    if (showLogs) {
        // 创建包含计算结果和中文标题的对象
        const resultsWithChineseTitles = {
            "借款利率": loanRate,
            "总教育费附加税率": totalEducationSurchargeRate
        };
        console.log("\n===== 关键指标 =====");
        console.table(resultsWithChineseTitles);
    }
    
    return {
        loanRate,
        totalEducationSurchargeRate
    };
}

module.exports = { calculateKeyIndicators }; 