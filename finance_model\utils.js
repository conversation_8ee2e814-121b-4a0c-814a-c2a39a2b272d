function SUM(array) {
    if (!array || !Array.isArray(array)) {
        return 0;
    }
    return array.reduce((sum, current) => sum + current, 0);
}

// NPV计算净现值
function NPV(rate, cashFlows) {
    if (!cashFlows || !Array.isArray(cashFlows)) {
        return 0;
    }
    let npv = 0;
    for (let i = 0; i < cashFlows.length; i++) {
        npv += cashFlows[i] / Math.pow(1 + rate, i);
    }
    return npv;
}

// IRR计算内部收益率
function IRR(cashFlows, guess = 0.1) {
    // 检查输入的有效性
    if (!cashFlows || !Array.isArray(cashFlows) || cashFlows.length === 0) {
        console.warn("IRR calculation: cashFlows is undefined, empty or not an array");
        return 0; // 返回0而不是NaN以避免后续计算错误
    }
    
    const EPSILON = 0.0000001;
    const MAX_ITERATIONS = 10000;
    
    // 牛顿法求解IRR
    let x = guess;
    let iteration = 0;
    
    while (iteration < MAX_ITERATIONS) {
        const fx = cashFlows.reduce((sum, cf, i) => sum + cf / Math.pow(1 + x, i), 0);
        if (Math.abs(fx) < EPSILON) {
            return x;
        }
        
        const dfx = cashFlows.reduce((sum, cf, i) => sum - i * cf / Math.pow(1 + x, i + 1), 0);
        if (Math.abs(dfx) < EPSILON) {
            // 避免除以接近0的值
            return x;
        }
        x = x - fx / dfx;
        iteration++;
    }
    
    // 如果没有收敛，返回0
    console.warn("IRR calculation did not converge after", MAX_ITERATIONS, "iterations");
    return 0;
}

module.exports = { SUM, NPV, IRR }; 