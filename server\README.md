# 财务测算API服务器

基于 Express.js 的财务测算后端服务器，提供财务测算计算接口。

## 功能特性

- ✅ 调用 `dist/run.js` 中的 `getFinanceData` 方法
- ✅ POST 接口接收参数并返回计算结果
- ✅ 完整的参数验证
- ✅ 详细的错误处理
- ✅ 健康检查接口
- ✅ CORS 支持
- ✅ 请求日志记录

## 快速开始

### 1. 安装依赖

```bash
npm install
# 或者
yarn install
```

### 2. 启动服务器

```bash
# 生产模式
npm run start:server

# 开发模式（自动重启）
npm run start:server:dev

# 或者直接运行
node server/index.js
```

### 3. 验证服务器

服务器启动后，访问以下地址验证：

- 服务器状态: http://localhost:3000/
- 健康检查: http://localhost:3000/api/finance/health
- API信息: http://localhost:3000/api/finance/info

## API 接口

### 1. 财务测算计算

**POST** `/api/finance/calculate`

计算财务测算结果

#### 请求格式

```json
{
  "inputs": {
    "baseLoanRate": 0.041,
    "operatingYears": 25,
    "config": {
      "epcInfo": {
        "pvEPC": 3.5,
        "windEPC": 5.2,
        "pvCapacity": 100,
        "windCapacity": 150,
        ...
      }
    },
    ...
  },
  "showLogs": false
}
```

#### 响应格式

成功响应:
```json
{
  "success": true,
  "data": {
    "resultTables": { ... },
    "resultAnalysis": [ ... ]
  },
  "message": "计算成功",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

错误响应:
```json
{
  "success": false,
  "message": "错误信息",
  "statusCode": 400,
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### 2. 健康检查

**GET** `/api/finance/health`

检查服务器运行状态

### 3. API信息

**GET** `/api/finance/info`

获取API详细信息和使用说明

## 使用示例

### 查看示例参数

```bash
npm run example
```

### JavaScript/Node.js 调用示例

```javascript
const axios = require('axios');

async function callFinanceAPI() {
  try {
    const response = await axios.post('http://localhost:3000/api/finance/calculate', {
      inputs: {
        baseLoanRate: 0.041,
        operatingYears: 25,
        config: {
          epcInfo: {
            pvEPC: 3.5,
            windEPC: 5.2,
            pvCapacity: 100,
            windCapacity: 150,
            // ... 其他参数
          }
        }
        // ... 更多参数
      },
      showLogs: false
    });
    
    console.log('计算结果:', response.data);
  } catch (error) {
    console.error('调用失败:', error.response?.data || error.message);
  }
}
```

### cURL 调用示例

```bash
curl -X POST http://localhost:3000/api/finance/calculate \
  -H "Content-Type: application/json" \
  -d '{
    "inputs": {
      "baseLoanRate": 0.041,
      "operatingYears": 25,
      "config": {
        "epcInfo": {
          "pvEPC": 3.5,
          "windEPC": 5.2,
          "pvCapacity": 100,
          "windCapacity": 150
        }
      }
    },
    "showLogs": false
  }'
```

## 测试

### 运行API测试

```bash
# 确保服务器已启动，然后运行测试
npm run test:api
```

测试将验证：
- 服务器基本功能
- 健康检查接口
- API信息接口
- 财务计算接口
- 参数验证功能

## 输入参数说明

### 必需参数

- `config.epcInfo`: EPC配置信息
- `baseLoanRate`: 基础借款利率
- `operatingYears`: 项目运营年限

### 主要参数分类

1. **基础参数**: 利率、年限、比例等
2. **设备容量**: 光伏、风电、储能、电解槽容量
3. **EPC单价**: 各设备的EPC单价
4. **运营参数**: 氢气价格、电价等
5. **成本参数**: 人工、材料、维修等成本
6. **税率参数**: 各种税率设置

详细的参数说明请查看 `server/example-input.js` 文件。

## 错误处理

服务器提供详细的错误信息：

- **400**: 参数验证错误
- **404**: 接口不存在
- **500**: 服务器内部错误

所有错误响应都包含：
- `success`: false
- `message`: 错误描述
- `statusCode`: HTTP状态码
- `timestamp`: 错误发生时间

## 日志记录

服务器会记录：
- 所有HTTP请求
- 计算开始和结束时间
- 错误信息和堆栈跟踪

## 性能说明

- 支持大参数量（50MB限制）
- 包含请求时间统计
- 异步处理，不阻塞其他请求

## 开发说明

### 项目结构

```
server/
├── index.js           # 主服务器文件
├── example-input.js   # 示例输入参数
├── test-api.js        # API测试脚本
└── README.md          # 本文档
```

### 扩展开发

如需添加新的接口或功能：

1. 在 `server/index.js` 中添加新的路由
2. 实现相应的处理逻辑
3. 更新 `server/test-api.js` 添加测试
4. 更新本文档

## 故障排除

### 常见问题

1. **服务器启动失败**
   - 检查端口3000是否被占用
   - 确认依赖已正确安装

2. **计算失败**
   - 检查输入参数格式
   - 确认 `dist/run.js` 文件存在
   - 查看服务器日志获取详细错误信息

3. **参数验证失败**
   - 参考 `server/example-input.js` 检查参数格式
   - 确保必需参数已提供

### 日志查看

服务器会在控制台输出详细日志，包括：
- 请求信息
- 计算时间
- 错误详情

如有问题，请查看控制台日志获取详细信息。 