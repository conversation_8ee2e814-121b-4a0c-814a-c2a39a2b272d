/**
 * 计算工程总概算表
 * @param {Object} params - 输入参数
 * @param {boolean} showLogs - 是否显示表格数据（可选，默认为false）
 * @returns {Object} - 工程总概算表结果
 */
function calculateProjectOverallBudget(params, showLogs = false) {
    const {
        mechanicalAndElectricalEquipment, // 机电设备及安装工程
        constructionWorks, // 建筑工程
        otherExpenses, // 其他费用
        fixedAssetsStaticInvestment, // 固定资产静态投资
        workingCapital, // 流动资金
        constructionPeriodInterest, // 建设期利息
        projectTotalInvestment, // 项目总投资
        installedCapacity // 装机量
    } = params;

    // 静态投资 = 固定资产静态投资
    const staticInvestment = fixedAssetsStaticInvestment;
    
    // 建设期补助
    const constructionPeriodSubsidy = 0;
    
    // 建设投资 = 静态投资 - 建设期补助
    const constructionInvestment = staticInvestment - constructionPeriodSubsidy;
    
    // 工程总投资 = 基本预备费 + 静态投资 + 价差预备费 + 建设期利息
    const basicReserveFund = 0; // 基本预备费
    const priceDifferenceReserveFund = 0; // 价差预备费
    const totalProjectInvestment = basicReserveFund + staticInvestment + priceDifferenceReserveFund + constructionPeriodInterest;
    
    // 单瓦动态总投资(单位：元/w) = (项目总投资 - 流动资金) * 10000 / (装机量 * 1000000)
    const dynamicInvestmentPerWatt = (projectTotalInvestment - workingCapital) * 10000 / (installedCapacity * 1000000);
    
    // 单瓦静态总投资(单位：元/w) = 固定资产静态投资 / 装机量 / 100
    const staticInvestmentPerWatt = fixedAssetsStaticInvestment / installedCapacity / 100;

   

    // 只有在showLogs为true时才打印表格
    if (showLogs) {
        // 创建包含结果和中文标题的对象
        const resultsWithChineseTitles = {
            "机电设备及安装工程": mechanicalAndElectricalEquipment,
            "建筑工程": constructionWorks,
            "其他费用": otherExpenses,
            "静态投资": staticInvestment,
            "建设期补助": constructionPeriodSubsidy,
            "建设投资": constructionInvestment,
            "建设期利息": constructionPeriodInterest,
            "工程总投资": totalProjectInvestment,
            "单瓦动态总投资(元/w)": dynamicInvestmentPerWatt,
            "单瓦静态总投资(元/w)": staticInvestmentPerWatt
        };
        console.log("\n===== 工程总概算表 =====");
        console.table(resultsWithChineseTitles);
    }

    // 输出结果
    const results = {
        mechanicalAndElectricalEquipment,
        constructionWorks,
        otherExpenses,
        staticInvestment,
        constructionPeriodSubsidy,
        constructionInvestment,
        constructionPeriodInterest,
        totalProjectInvestment,
        dynamicInvestmentPerWatt,
        staticInvestmentPerWatt
    };

    return results;
}

module.exports = { calculateProjectOverallBudget }; 