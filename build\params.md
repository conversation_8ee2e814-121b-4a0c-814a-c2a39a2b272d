## 0.计算关键指标
### 入参
```js
const testInputs = {
  // 关键指标
  baseLoanRate: 0.035, // 基础借款利率
  rateChangeCoefficient: 1, // 利率变化系数
  VATRate: 0.13, // 增值税率
  installationTaxRate: 0.09, // 安装税率
  incomeTaxRate: 0.25, // 所得税率
  equityRatio: 0.3, // 资本金比例（1-融资比例）
  financingRatioBase: 0.7, // 融资比例基准
  projectChangeFactor: 1, // 项目总概算变化系数
  fixedAssetsFinancialFactor: 1, // 财务测算固定资产敏感系数
  discountRate: 0.05, // 折现系数
  
  // 项目参数
  installedCapacity: 400, // 装机量(单位：MW)
  operatingYears: 25, // 项目运营年限
  loanTerm: 15, // 贷款期限(单位：年)
  firstYearPowerGenerationHour: 1235, // 首年发电小时数
  powerToHydrogenRatio: 1, // 发电制氢比例
  
  // 设备容量拆分
  windTurbineCapacity: 400 * 0.4, // 风机容量(单位：MW)
  photovoltaicCapacity: 400 * 0.6, // 光伏容量(单位：MW)
  
  // 拆分后设备成本
  // 发电侧设备
  windTurbineEquipment: 118876.913541 * 0.3, // 风机设备(单位：万元)
  photovoltaicEquipment: 118876.913541 * 0.5, // 光伏设备(单位：万元)
  energyStorageEquipment: 118876.913541 * 0.2, // 储能设备(单位：万元)
  
  // 发电侧安装
  windTurbineInstallation: 8248.4086632 * 0.3, // 风机安装(单位：万元)
  photovoltaicInstallation: 8248.4086632 * 0.5, // 光伏安装(单位：万元)
  energyStorageInstallation: 8248.4086632 * 0.2, // 储能安装(单位：万元)
  
  // 发电侧建筑
  windTurbineBuilding: 8573.425446 * 0.3, // 风机建筑(单位：万元)
  photovoltaicBuilding: 8573.425446 * 0.5, // 光伏建筑(单位：万元)
  energyStorageBuilding: 8573.425446 * 0.2, // 储能建筑(单位：万元)
  
  // TODO: 发电侧其他
  windTurbineOthers: 12300.3351398127 * 0.3, // 风机其他(单位：万元)
  photovoltaicOthers: 12300.3351398127 * 0.5, // 光伏其他(单位：万元)
  energyStorageOthers: 12300.3351398127 * 0.2, // 储能其他(单位：万元)
  
  // 制氢侧设备
  electrolyzerEquipment: 37075.4258 * 0.8, // 电解槽设备(单位：万元)
  hydrogenStorageEquipment: 37075.4258 * 0.2, // 储罐设备(单位：万元)
  
  // 制氢侧安装
  electrolyzerInstallation: 5217.913675 * 0.8, // 电解槽安装(单位：万元)
  hydrogenStorageInstallation: 5217.913675 * 0.2, // 储罐安装(单位：万元)
  
  // 制氢侧建筑
  electrolyzerBuilding: 20417 * 0.8, // 电解槽建筑(单位：万元)
  hydrogenStorageBuilding: 20417 * 0.2, // 储罐建筑(单位：万元)
  
  // 制氢侧其他
  electrolyzerOthers: 2220 * 0.8, // 电解槽其他(单位：万元)
  hydrogenStorageOthers: 2220 * 0.2, // 储罐其他(单位：万元)
  
  // 运营参数
  electrolyzerPowerConsumption: 5, // 电解槽电耗
  hydrogenPriceWithTax: 25, // 运营期氢气售价(元/公斤)
  hydrogenPriceNoTax: 20, // 氢气售价不含税(元/kg)
  oxygenPriceNoTax: 0, // 氧气售价不含税(元/kg)
  oxygenPriceWithTax: 0, // 氧气售价含增值税(元/kg)
  gridElectricityPriceWithTax: 0.2829, // 脱硫煤电价含增值税(元/kwh)
  gridElectricityPriceNoTax: 0.2504, // 脱硫煤电价不含税(元/kwh)
  
  // 运营成本相关
  salvageRate: 0.05, // 残值率
  waterPriceNoTax: 2.2, // 水费单价不含税(单位：元/吨)
  selfPowerPlantElectricityPriceWithTax: 0.1814, // 自备电厂含税电价(单位：元/kwh)
  selfPowerPlantElectricityPriceNoTax: 0, // 自备电厂不含税电价(单位：元/kwh)
  gridHydrogenElectricityPriceNoTax: 0, // 下网不含税电价(单位：元/kwh)
  transportationRateNoTax: 0, // 运输费率不含税(单位：元/kg/公里)
  transportDistance: 0, // 运输距离(单位：公里)

  // 折旧年限
  windTurbineDepreciationYears: 20, // 风机折旧年限
  photovoltaicDepreciationYears: 20, // 光伏折旧年限
  energyStorageDepreciationYears: 10, // 储能折旧年限
  electrolyzerDepreciationYears: 20, // 电解槽折旧年限
  hydrogenStorageDepreciationYears: 20, // 储氢折旧年限
  
  // 材料费相关（拆分）
  // 发电侧材料费
  windTurbineMaterialsCostProvisionRate: 0.003, // 风机材料费计提率
  photovoltaicMaterialsCostProvisionRate: 0.003, // 光伏材料费计提率
  energyStorageMaterialsCostProvisionRate: 0.003, // 储能材料费计提率
  windTurbineMaterialsCostAnnualGrowthRate: 0.015, // 风机材料费年均增长率
  photovoltaicMaterialsCostAnnualGrowthRate: 0.015, // 光伏材料费年均增长率
  energyStorageMaterialsCostAnnualGrowthRate: 0.015, // 储能材料费年均增长率
  
  // 制氢侧材料费
  electrolyzerMaterialsCostProvisionRate: 0.003, // 电解槽材料费计提率
  hydrogenStorageMaterialsCostProvisionRate: 0.003, // 储氢材料费计提率
  electrolyzerMaterialsCostAnnualGrowthRate: 0.015, // 电解槽材料费年均增长率
  hydrogenStorageMaterialsCostAnnualGrowthRate: 0.015, // 储氢材料费年均增长率
      
  // 大修更换费率（拆分）
  windTurbineMajorOverhaulReplacementRate: 0.3, // 风机大修更换费率
  photovoltaicMajorOverhaulReplacementRate: 0.3, // 光伏大修更换费率
  energyStorageMajorOverhaulReplacementRate: 0.3, // 储能大修更换费率
  electrolyzerMajorOverhaulReplacementRate: 0.3, // 电解槽大修更换费率
  hydrogenStorageMajorOverhaulReplacementRate: 0.3, // 储氢大修更换费率
  
  // 土地租赁费
  windTurbineLandRentalFeeYear2: 337.5 * 0.4, // 风机第2年土地租赁费(单位：万元)
  photovoltaicLandRentalFeeYear2: 337.5 * 0.6, // 光伏第2年土地租赁费(单位：万元)
  windTurbineLandRentalFeeGrowthRate: 0.01, // 风机土地租赁费增长率
  photovoltaicLandRentalFeeGrowthRate: 0.01, // 光伏土地租赁费增长率
  
  // 土地税费
  windTurbineLandTaxAnnual: 9.7638 * 0.4, // 风机年度土地税费(单位：万元)
  photovoltaicLandTaxAnnual: 9.7638 * 0.6, // 光伏年度土地税费(单位：万元)
  
  // 各组件其他费用单价
  windTurbineOtherCostUnitPrice: 1, // 风机其他费用单价(元/kw)
  photovoltaicOtherCostUnitPrice: 1.5, // 光伏其他费用单价(元/kw)
  energyStorageOtherCostUnitPrice: 0.5, // 储能其他费用单价(元/kw)
  electrolyzerOtherCostUnitPrice: 0.4, // 电解槽其他费用单价(元/kw)
  hydrogenStorageOtherCostUnitPrice: 0.1, // 储氢其他费用单价(元/kw)
  
  // 费用增长率
  windTurbineOtherCostGrowthRate: 0.03, // 风机其他费用年增长率
  photovoltaicOtherCostGrowthRate: 0.03, // 光伏其他费用年增长率
  energyStorageOtherCostGrowthRate: 0.03, // 储能其他费用年增长率
  electrolyzerOtherCostGrowthRate: 0.03, // 电解槽其他费用年增长率
  hydrogenStorageOtherCostGrowthRate: 0.03, // 储氢其他费用年增长率
  
  
  // 人工成本相关
  workerCount: 12, // 工人数量
  workerMonthlySalary: 0.55, // 工人人均月薪
  technicianCount: 3, // 技术人员数量
  technicianMonthlySalary: 0.65, // 技术人均月薪
  managerCount: 2, // 管理人员数量
  managerMonthlySalary: 0.72, // 管理人均月薪
  socialSecurityAndWelfareFactor: 0.5, // 福利社保系数
  salaryAnnualGrowthRate: 0.05, // 薪资年增长率
  
  waterResourceTaxUnitPrice: 0.7, // 水资源税单价(单位：元/吨)
  // 税率相关
  maintenanceCostVATRate: 0.13, // 维修费增值税率
  materialsCostVATRate: 0.13, // 材料费增值税率
  transportationFeeVATRate: 0.09, // 运输费增值税率
  waterFeeVATRate: 0.09, // 水费增值税率
  electricityFeeVATRate: 0.13, // 电费增值税率
  revenueItemVATRate: 0.13, // 收入项增值税率
  cityConstructionAndMaintenanceTaxRate: 0.05, // 城市建设维护税率
  educationSurchargeRate: 0.03, // 教育费附加税率
  localEducationSurchargeRate: 0.02, // 地方教育费附加税率
  statutorySurplusReserveFundRate: 0.1, // 法定盈余公积金率
  shareholderDividendRatio: 0, // 股东分红占比
  
  // 折旧率、维修费和保险费率相关
  // 发电侧维修费率
  windTurbineMaintCostRateYear2: 0.004, // 风机第2年维修费率
  photovoltaicMaintCostRateYear2: 0.004, // 光伏第2年维修费率
  energyStorageMaintCostRateYear2: 0.004, // 储能第2年维修费率
  
  // 发电侧维修费增长率
  windTurbineMaintCostGrowthRateYears7to11: 0.015, // 风机第7到11年维修费年增长率
  windTurbineMaintCostGrowthRateYears12plus: 0.02, // 风机第12到N年维修费年增长率
  photovoltaicMaintCostGrowthRateYears7to11: 0.015, // 光伏第7到11年维修费年增长率
  photovoltaicMaintCostGrowthRateYears12plus: 0.02, // 光伏第12到N年维修费年增长率
  energyStorageMaintCostGrowthRateYears7to11: 0.015, // 储能第7到11年维修费年增长率
  energyStorageMaintCostGrowthRateYears12plus: 0.02, // 储能第12到N年维修费年增长率
  
  // 制氢侧维修费率
  electrolyzerMaintCostRateYear2: 0.005, // 电解槽第2年维修费率
  hydrogenStorageMaintCostRateYear2: 0.005, // 储氢第2年维修费率
  
  // 制氢侧维修费增长率
  electrolyzerMaintCostGrowthRateYears7to11: 0.01, // 电解槽第7到11年维修费年增长率
  electrolyzerMaintCostGrowthRateYears12plus: 0.01, // 电解槽第12到N年维修费年增长率
  hydrogenStorageMaintCostGrowthRateYears7to11: 0.01, // 储氢第7到11年维修费年增长率
  hydrogenStorageMaintCostGrowthRateYears12plus: 0.01, // 储氢第12到N年维修费年增长率
  
  // 保险费率
  windTurbineInsuranceRate: 0.0005, // 风机保险费率
  photovoltaicInsuranceRate: 0.0005, // 光伏保险费率
  energyStorageInsuranceRate: 0.0005, // 储能保险费率
  electrolyzerInsuranceRate: 0.0005, // 电解槽保险费率
  hydrogenStorageInsuranceRate: 0.0005, // 储氢保险费率
  
  // 年衰减相关
  yearTwoDegradationPercentage: 0.02, // 第2年衰减百分值
  yearNDegradationPercentage: 0.0055, // 第N年衰减百分值
  
  // 敏感系数
  powerGenerationCostSensitivityFactor: 1, // 发电成本敏感系数
  hydrogenProductionCostSensitivityFactor: 1, // 制氢成本敏感系数
  totalCostSensitivityFactor: 1, // 总成本敏感系数

  // 下网
  firstYearDownGridPower: 0.1283, // 首年下网电量
  genH2ConsumeWater: 2, // 制氢耗水量(L/Nm³) // 一般：1.2 ～ 2.0 L/Nm 
  publicLoadCapacity: 0, // 公辅装置功率(MW) 18.438
  publicLoadHour: 8000, // 公辅装置运行小时数
  config: {
    // 来自容量系统
    isFromCapacity: false,
    capacity: {
      firstYearGreenPower: 21378, // 首年绿电发电量 0 (万度)
      firstYearHydrogenElectricity: 17602, // 首年绿电制氢用电量(万度)
      firstYearUpGridPower: 21378 * 0.16, // 首年上网电量(万度)
      firstYearHydrogenProduction: 333.5, // 首年绿电制氢量(万kg)
      firstYearGridHydrogenProduction: 24.3, // 首年电网制氢量(万kg)
    },
    epcInfo: {
      pvEPC: 3.5, // 元/W
      windEPC: 3.6,
      batEPC: 1.25,
      alkEPC: 3.2,
      alkStoreEPC: 2500, // 元/m3
    
      pvCapacity: 425, // MW
      windCapacity: 0,
      batCapacity: 63,
      alkCapacity: 225,
      alkStoreCapacity: 0, // m³
      
      // pv ratio
      pvEquipmentRatio: 0.8032,
      pvInstallRatio: 0.05573,
      pvBuildingRatio: 0.05793,
      pvOthersRatio: 0.08311,
  
      // wind ratio
      windEquipmentRatio: 0.8032,
      windInstallRatio: 0.05573,
      windBuildingRatio: 0.05793,
      windOthersRatio: 0.08311,
  
      // bat ratio
      batEquipmentRatio: 0.8032,
      batInstallRatio: 0.05573,
      batBuildingRatio:0.05793,
      batOthersRatio: 0.08311,
  
  
      // alk ratio
      alkEquipmentRatio: 0.571003,
      alkInstallRatio: 0.0803617,
      alkBuildingRatio: 0.314444,
      alkOthersRatio:  0.03419,
  
      // alkStore ratio
      alkStoreEquipmentRatio: 0.571003,
      alkStoreInstallRatio: 0.0803617,
      alkStoreBuildingRatio: 0.314444,
      alkStoreOthersRatio: 0.03419,
  
      // 土地租赁单价
      pvLandUnitPrice: 0.008425, // 元/W 
      windLandUnitPrice: 0.008425, // 元/W 
    },
    initInfo: {
      totalCost: [
        // { key: 'laborCost', value: new Array(20).fill(8) }
      ]
    },
  }
};
```
### 出参
```js
const result = {
        loanRate,
        totalEducationSurchargeRate
    }
```
## 1. 计算工程总概算表
### 入参
```js
    const {
        mechanicalAndElectricalEquipment, // 机电设备及安装工程
        constructionWorks, // 建筑工程
        otherExpenses, // 其他费用
        fixedAssetsStaticInvestment, // 固定资产静态投资
        workingCapital, // 流动资金
        constructionPeriodInterest, // 建设期利息
        projectTotalInvestment, // 项目总投资
        installedCapacity // 装机量
    } = params;
```
### 出参
```js
    const results = {
        mechanicalAndElectricalEquipment,
        constructionWorks,
        otherExpenses,
        staticInvestment,
        constructionPeriodSubsidy,
        constructionInvestment,
        constructionPeriodInterest,
        totalProjectInvestment,
        dynamicInvestmentPerWatt,
        staticInvestmentPerWatt
    };
```

## 2. 计算融资计划
### 入参
```js
 const {
        projectTotalInvestment, // 项目总投资
        financingRatioBase // 融资比例基准
    } = params;
```

### 出参
```js
   const results = {
        projectTotalInvestment,
        bankLoan,
        equityCapital
    };
```

## 3.计算固定资产投资估算 
### 入参
```js
const {
        // 拆分后的风机参数
        windTurbineEquipment,   // 风机设备
        windTurbineInstallation, // 风机安装
        windTurbineBuilding,    // 风机建筑
        windTurbineOthers,      // 风机其他
        
        // 拆分后的光伏参数
        photovoltaicEquipment,   // 光伏设备
        photovoltaicInstallation, // 光伏安装
        photovoltaicBuilding,    // 光伏建筑
        photovoltaicOthers,      // 光伏其他
        
        // 拆分后的储能参数
        energyStorageEquipment,   // 储能设备
        energyStorageInstallation, // 储能安装
        energyStorageBuilding,    // 储能建筑
        energyStorageOthers,      // 储能其他
        
        // 拆分后的电解槽参数
        electrolyzerEquipment,   // 电解槽设备
        electrolyzerInstallation, // 电解槽安装
        electrolyzerBuilding,    // 电解槽建筑
        electrolyzerOthers,      // 电解槽其他
        
        // 拆分后的储罐参数
        hydrogenStorageEquipment,   // 储罐设备
        hydrogenStorageInstallation, // 储罐安装
        hydrogenStorageBuilding,    // 储罐建筑
        hydrogenStorageOthers,      // 储罐其他
        
        // 其他参数
        projectChangeFactor, // 项目总概算变化系数
        fixedAssetsFinancialFactor, // 财务测算固定资产敏感系数
        VATRate, // 成本项电费增值税
        installationTaxRate, // 安装税
        installedCapacity, // 装机量
        loanRate, // 借贷利率
        financingRatioBase = 0.7 // 融资比例基准，默认70%
    } = params;

```

### 出参
```js
  // 输出结果，保持原有结构
    const results = {
        mechanicalAndElectricalEquipment,
        constructionWorks,
        otherExpenses,
        fixedAssetsStaticInvestment,
        workingCapital,
        constructionPeriodInterest,
        projectTotalInvestment,
        fixedPowerAssetsNoTax,
        fixedHydrogenAssetsNoTax,
        fixedAssetsNoTax,
        fixedPowerAssetsWithTax,
        fixedHydrogenAssetsWithTax,
        fixedAssetsWithTax,
        equipmentAndInstallationNoTax,
        powerEquipmentAndInstallationNoTax,
        powerEquipmentAndInstallationWithTax,
        hydrogenEquipmentAndInstallationNoTax,
        hydrogenEquipmentAndInstallationWithTax,
        equipmentPurchaseCost,
        installationCost,
        
        // 拆分组件结果
        windTurbineEquipmentAndInstallationNoTax,
        photovoltaicEquipmentAndInstallationNoTax,
        energyStorageEquipmentAndInstallationNoTax,
        electrolyzerEquipmentAndInstallationNoTax,
        hydrogenStorageEquipmentAndInstallationNoTax,
        
        fixedWindTurbineAssetsNoTax,
        fixedPhotovoltaicAssetsNoTax,
        fixedEnergyStorageAssetsNoTax,
        fixedElectrolyzerAssetsNoTax,
        fixedHydrogenStorageAssetsNoTax,
        
        windTurbineEquipmentAndInstallationWithTax,
        photovoltaicEquipmentAndInstallationWithTax,
        energyStorageEquipmentAndInstallationWithTax,
        electrolyzerEquipmentAndInstallationWithTax,
        hydrogenStorageEquipmentAndInstallationWithTax,
        
        fixedWindTurbineAssetsWithTax,
        fixedPhotovoltaicAssetsWithTax,
        fixedEnergyStorageAssetsWithTax,
        fixedElectrolyzerAssetsWithTax,
        fixedHydrogenStorageAssetsWithTax
    };
```

## 4.计算投资计划与资金筹措表 
### 入参
```js
const {
        projectTotalInvestment, // 项目总投资
        fixedAssetsStaticInvestment, // 固定资产静态投资
        constructionPeriodInterest, // 建设期利息 - 从固定资产投资估算模块获取
        workingCapital, // 流动资金
        bankLoan, // 银行借款
        equityCapital, // 资本金
        financingRatioBase, // 融资比例基准
        loanRate // 借款利率
    } = params;
```

### 出参
```js
   // 输出结果
    const results = {
        projectTotalInvestment,
        fixedAssetsStaticInvestment,
        foreignInvestment,
        domesticInvestment,
        constructionPeriodInterest,
        foreignInterestDuringConstruction,
        domesticInterestDuringConstruction,
        workingCapital,
        fundRaising,
        equityCapital,
        bankLoan,
        longTermLoan,
        domesticLoan,
        foreignLoan,
        constructionPeriodInterestLoan,
        workingCapitalLoan
    };
```

## 5.计算总成本费用表 
### 入参
```js
 const {
        operatingYears, // 项目运营年限
        incomeTaxRate, // 所得税率
        salvageRate, // 残值率
        windTurbineDepreciationYears, // 风机折旧年限
        photovoltaicDepreciationYears, // 光伏折旧年限
        energyStorageDepreciationYears, // 储能折旧年限
        electrolyzerDepreciationYears, // 电解槽折旧年限
        hydrogenStorageDepreciationYears, // 储氢折旧年限
            
        // 拆分发电侧资产
        fixedWindTurbineAssetsNoTax, // 固定风机资产不含税
        fixedPhotovoltaicAssetsNoTax, // 固定光伏资产不含税
        fixedEnergyStorageAssetsNoTax, // 固定储能资产不含税
        windTurbineEquipmentAndInstallationWithTax, // 风机设备及安装含税
        photovoltaicEquipmentAndInstallationWithTax, // 光伏设备及安装含税
        energyStorageEquipmentAndInstallationWithTax, // 储能设备及安装含税
        fixedWindTurbineAssetsWithTax, // 固定风机资产含税
        fixedPhotovoltaicAssetsWithTax, // 固定光伏资产含税
        fixedEnergyStorageAssetsWithTax, // 固定储能资产含税
        windTurbineEquipmentAndInstallationNoTax, // 风机设备及安装不含税
        photovoltaicEquipmentAndInstallationNoTax, // 光伏设备及安装不含税
        energyStorageEquipmentAndInstallationNoTax, // 储能设备及安装不含税
        
        // 拆分制氢侧资产
        fixedElectrolyzerAssetsNoTax, // 固定电解槽资产不含税
        fixedHydrogenStorageAssetsNoTax, // 固定储罐资产不含税
        electrolyzerEquipmentAndInstallationWithTax, // 电解槽设备及安装含税
        hydrogenStorageEquipmentAndInstallationWithTax, // 储罐设备及安装含税
        fixedElectrolyzerAssetsWithTax, // 固定电解槽资产含税
        fixedHydrogenStorageAssetsWithTax, // 固定储罐资产含税
        electrolyzerEquipmentAndInstallationNoTax, // 电解槽设备及安装不含税
        hydrogenStorageEquipmentAndInstallationNoTax, // 储罐设备及安装不含税
        
        // 土地租赁费和税费
        windTurbineLandRentalFeeYear2, // 风机第2年土地租赁费
        photovoltaicLandRentalFeeYear2, // 光伏第2年土地租赁费
        windTurbineLandRentalFeeGrowthRate, // 风机土地租赁费增长率
        photovoltaicLandRentalFeeGrowthRate, // 光伏土地租赁费增长率
        windTurbineLandTaxAnnual, // 风机年度土地税费
        photovoltaicLandTaxAnnual, // 光伏年度土地税费
        
        // 各组件其他费用单价
        windTurbineOtherCostUnitPrice, // 风机其他费用单价
        photovoltaicOtherCostUnitPrice, // 光伏其他费用单价
        energyStorageOtherCostUnitPrice, // 储能其他费用单价
        electrolyzerOtherCostUnitPrice, // 电解槽其他费用单价
        hydrogenStorageOtherCostUnitPrice, // 储氢其他费用单价
        
        // 各组件其他费用增长率
        windTurbineOtherCostGrowthRate, // 风机其他费用年增长率
        photovoltaicOtherCostGrowthRate, // 光伏其他费用年增长率
        energyStorageOtherCostGrowthRate, // 储能其他费用年增长率
        electrolyzerOtherCostGrowthRate, // 电解槽其他费用年增长率
        hydrogenStorageOtherCostGrowthRate, // 储氢其他费用年增长率
        
        // 其他参数
        waterConsumption, // 第N年耗水量
        waterPriceNoTax, // 水费单价不含税
        greenHydrogenElectricity, // 第N年绿电制氢电量
        selfPowerPlantElectricityPriceNoTax, // 自备电厂不含税电价
        gridHydrogenElectricity, // 第N年下网制氢电量
        gridHydrogenElectricityPriceNoTax, // 下网不含税电价
        selfPowerPlantElectricityPriceWithTax, // 自备电厂含税电价
        hydrogenProduction, // 第N年制氢量
        
        // 拆分后的材料费率
        windTurbineMaterialsCostProvisionRate, // 风机材料费计提率
        photovoltaicMaterialsCostProvisionRate, // 光伏材料费计提率
        energyStorageMaterialsCostProvisionRate, // 储能材料费计提率
        electrolyzerMaterialsCostProvisionRate, // 电解槽材料费计提率
        hydrogenStorageMaterialsCostProvisionRate, // 储氢材料费计提率
        
        // 拆分后的材料费增长率
        windTurbineMaterialsCostAnnualGrowthRate, // 风机材料费年均增长率
        photovoltaicMaterialsCostAnnualGrowthRate, // 光伏材料费年均增长率
        energyStorageMaterialsCostAnnualGrowthRate, // 储能材料费年均增长率
        electrolyzerMaterialsCostAnnualGrowthRate, // 电解槽材料费年均增长率
        hydrogenStorageMaterialsCostAnnualGrowthRate, // 储氢材料费年均增长率
        
        totalCostSensitivityFactor, // 总成本敏感系数
        transportationRateNoTax, // 运输费率不含税
        transportDistance, // 运输距离
        
        // 拆分后的大修更换费率
        windTurbineMajorOverhaulReplacementRate, // 风机大修更换费率
        photovoltaicMajorOverhaulReplacementRate, // 光伏大修更换费率
        energyStorageMajorOverhaulReplacementRate, // 储能大修更换费率
        electrolyzerMajorOverhaulReplacementRate, // 电解槽大修更换费率
        hydrogenStorageMajorOverhaulReplacementRate, // 储氢大修更换费率
        
        installedCapacity, // 装机量
        powerGenerationCostSensitivityFactor, // 发电成本敏感系数
        hydrogenProductionCostSensitivityFactor, // 制氢成本敏感系数
        waterResourceTaxUnitPrice, // 水资源税单价
        workerCount, // 工人数量
        workerMonthlySalary, // 工人人均月薪
        technicianCount, // 技术人员数量
        technicianMonthlySalary, // 技术人均月薪
        managerCount, // 管理人员数量
        managerMonthlySalary, // 管理人均月薪
        socialSecurityAndWelfareFactor, // 福利社保系数
        salaryAnnualGrowthRate, // 薪资年增长率
        maintenanceCostVATRate, // 维修费增值税率
        materialsCostVATRate, // 材料费增值税率
        transportationFeeVATRate, // 运输费增值税率
        waterFeeVATRate, // 水费增值税率
        electricityFeeVATRate, // 电费增值税率
        annualInterest, // 第N年本年付息
        shortTermLoanInterest, // 第N年短期借款利息
        workingCapitalInterest, // 第N年流动资金利息
        
        // 拆分后的维修费率
        windTurbineMaintCostRateYear2, // 风机第2年维修费率
        photovoltaicMaintCostRateYear2, // 光伏第2年维修费率
        energyStorageMaintCostRateYear2, // 储能第2年维修费率
        electrolyzerMaintCostRateYear2, // 电解槽第2年维修费率
        hydrogenStorageMaintCostRateYear2, // 储氢第2年维修费率
        
        // 拆分后的维修费增长率
        windTurbineMaintCostGrowthRateYears7to11, // 风机第7到11年维修费年增长率
        windTurbineMaintCostGrowthRateYears12plus, // 风机第12到N年维修费年增长率
        photovoltaicMaintCostGrowthRateYears7to11, // 光伏第7到11年维修费年增长率
        photovoltaicMaintCostGrowthRateYears12plus, // 光伏第12到N年维修费年增长率
        energyStorageMaintCostGrowthRateYears7to11, // 储能第7到11年维修费年增长率
        energyStorageMaintCostGrowthRateYears12plus, // 储能第12到N年维修费年增长率
        electrolyzerMaintCostGrowthRateYears7to11, // 电解槽第7到11年维修费年增长率
        electrolyzerMaintCostGrowthRateYears12plus, // 电解槽第12到N年维修费年增长率
        hydrogenStorageMaintCostGrowthRateYears7to11, // 储氢第7到11年维修费年增长率
        hydrogenStorageMaintCostGrowthRateYears12plus, // 储氢第12到N年维修费年增长率
        
        // 拆分后的保险费率
        windTurbineInsuranceRate, // 风机保险费率
        photovoltaicInsuranceRate, // 光伏保险费率
        energyStorageInsuranceRate, // 储能保险费率
        electrolyzerInsuranceRate, // 电解槽保险费率
        hydrogenStorageInsuranceRate, // 储氢保险费率

        publicLoadCapacity, // 公辅装置容量
        publicLoadHour, // 公辅装置运行小时数
        config
    } = params;
```

### 出参
```js
  const results = {
        incomeTaxRate: new Array(years).fill(0), // 第N年所得税率
        
        // 原始折旧费用结果
        depreciation: new Array(years).fill(0), // 第N年折旧费
        powerDepreciation: new Array(years).fill(0), // 第N年发电折旧费
        hydrogenDepreciation: new Array(years).fill(0), // 第N年制氢折旧费
        
        // 拆分后的折旧费用结果
        windTurbineDepreciation: new Array(years).fill(0), // 第N年风机折旧费
        photovoltaicDepreciation: new Array(years).fill(0), // 第N年光伏折旧费
        energyStorageDepreciation: new Array(years).fill(0), // 第N年储能折旧费
        electrolyzerDepreciation: new Array(years).fill(0), // 第N年电解槽折旧费
        hydrogenStorageDepreciation: new Array(years).fill(0), // 第N年储罐折旧费
        
        // 原始维修费用结果
        maintenanceCost: new Array(years).fill(0), // 第N年维修费
        powerMaintenanceCost: new Array(years).fill(0), // 第N年发电维修费
        hydrogenMaintenanceCost: new Array(years).fill(0), // 第N年制氢维修费
        
        // 拆分后的维修费用结果
        windTurbineMaintenanceCost: new Array(years).fill(0), // 第N年风机维修费
        photovoltaicMaintenanceCost: new Array(years).fill(0), // 第N年光伏维修费
        energyStorageMaintenanceCost: new Array(years).fill(0), // 第N年储能维修费
        electrolyzerMaintenanceCost: new Array(years).fill(0), // 第N年电解槽维修费
        hydrogenStorageMaintenanceCost: new Array(years).fill(0), // 第N年储罐维修费
        
        // 原始保险费用结果
        insuranceCost: new Array(years).fill(0), // 第N年保险费
        powerInsuranceCost: new Array(years).fill(0), // 第N年发电保险费
        hydrogenInsuranceCost: new Array(years).fill(0), // 第N年制氢保险费
        
        // 拆分后的保险费用结果
        windTurbineInsuranceCost: new Array(years).fill(0), // 第N年风机保险费
        photovoltaicInsuranceCost: new Array(years).fill(0), // 第N年光伏保险费
        energyStorageInsuranceCost: new Array(years).fill(0), // 第N年储能保险费
        electrolyzerInsuranceCost: new Array(years).fill(0), // 第N年电解槽保险费
        hydrogenStorageInsuranceCost: new Array(years).fill(0), // 第N年储罐保险费
        
        // 原始水费结果
        waterCost: new Array(years).fill(0), // 第N年水费
        hydrogenWaterCost: new Array(years).fill(0), // 第N年制氢水费
        
        // 拆分后的水费结果 - 只有电解槽用水
        electrolyzerWaterCost: new Array(years).fill(0), // 第N年电解槽水费
        
        // 原始电费结果
        electricityCost: new Array(years).fill(0), // 第N年电费
        hydrogenElectricityCost: new Array(years).fill(0), // 第N年制氢电费
        
        // 拆分后的电费结果 - 只有电解槽用电
        electrolyzerElectricityCost: new Array(years).fill(0), // 第N年电解槽电费
        
        // 原始材料费结果
        materialsCost: new Array(years).fill(0), // 第N年材料费
        
        // 拆分后的材料费结果
        windTurbineMaterialsCost: new Array(years).fill(0), // 第N年风机材料费
        photovoltaicMaterialsCost: new Array(years).fill(0), // 第N年光伏材料费
        energyStorageMaterialsCost: new Array(years).fill(0), // 第N年储能材料费
        electrolyzerMaterialsCost: new Array(years).fill(0), // 第N年电解槽材料费
        hydrogenStorageMaterialsCost: new Array(years).fill(0), // 第N年储罐材料费
        
        // 原始运输费用结果
        transportationCost: new Array(years).fill(0), // 第N年运输成本
        hydrogenTransportationCost: new Array(years).fill(0), // 第N年氢气运输成本
        
        // 拆分后的运输费用结果 - 只有电解槽产品需要运输
        electrolyzerTransportationCost: new Array(years).fill(0), // 第N年电解槽产品运输成本
        
        // 原始设备大修更换费用结果
        equipmentOverhaulReplacementCost: new Array(years).fill(0), // 第N年设备大修更换费用
        
        // 拆分后的设备大修更换费用结果
        windTurbineOverhaulReplacementCost: new Array(years).fill(0), // 第N年风机设备大修更换费用
        photovoltaicOverhaulReplacementCost: new Array(years).fill(0), // 第N年光伏设备大修更换费用
        energyStorageOverhaulReplacementCost: new Array(years).fill(0), // 第N年储能设备大修更换费用
        electrolyzerOverhaulReplacementCost: new Array(years).fill(0), // 第N年电解槽设备大修更换费用
        hydrogenStorageOverhaulReplacementCost: new Array(years).fill(0), // 第N年储罐设备大修更换费用
        
        // 原始土地费用结果
        landRentalFee: new Array(years).fill(0), // 第N年土地租赁费
        povertyAlleviationFee: new Array(years).fill(0), // 第N年扶贫规费支出
        landTaxFee: new Array(years).fill(0), // 第N年土地税费
        
        // 拆分后的土地费用结果 - 只有风机和光伏需要土地费用
        windTurbineLandRentalFee: new Array(years).fill(0), // 第N年风机土地租赁费
        photovoltaicLandRentalFee: new Array(years).fill(0), // 第N年光伏土地租赁费
        windTurbineLandTaxFee: new Array(years).fill(0), // 第N年风机土地税费
        photovoltaicLandTaxFee: new Array(years).fill(0), // 第N年光伏土地税费
        
        // 原始其他费用结果
        otherCost: new Array(years).fill(0), // 第N年其他费用
        powerOtherCost: new Array(years).fill(0), // 第N年发电其他费用
        hydrogenOtherCost: new Array(years).fill(0), // 第N年制氢其他费用
        
        // 拆分后的其他费用结果
        windTurbineOtherCost: new Array(years).fill(0), // 第N年风机其他费用
        photovoltaicOtherCost: new Array(years).fill(0), // 第N年光伏其他费用
        energyStorageOtherCost: new Array(years).fill(0), // 第N年储能其他费用
        electrolyzerOtherCost: new Array(years).fill(0), // 第N年电解槽其他费用
        hydrogenStorageOtherCost: new Array(years).fill(0), // 第N年储罐其他费用

        publicLoadPowerCost: new Array(years).fill(0), // 第N年公辅装置电费
        
        // 原始水资源税结果
        waterResourceTax: new Array(years).fill(0), // 第N年水资源税
        
        // 拆分后的水资源税结果 - 只有电解槽需要水资源税
        electrolyzerWaterResourceTax: new Array(years).fill(0), // 第N年电解槽水资源税
        
        // 其他原始结果
        laborCost: new Array(years).fill(0), // 第N年工资福利及劳保统筹和住房基金
        amortizationCost: new Array(years).fill(0), // 第N年摊销费
        maintenanceCostInputTax: new Array(years).fill(0), // 第N年维修费进项税
        materialsCostInputTax: new Array(years).fill(0), // 第N年材料成本进项税
        transportationCostInputTax: new Array(years).fill(0), // 第N年运输成本进项税
        waterCostInputTax: new Array(years).fill(0), // 第N年水费进项税
        electricityCostInputTax: new Array(years).fill(0), // 第N年电费进项税
        inputTax: new Array(years).fill(0), // 第N年进项税
        interestExpenseWithTax: new Array(years).fill(0), // 第N年含税利息支出
        fixedCost: new Array(years).fill(0), // 第N年固定成本
        variableCost: new Array(years).fill(0), // 第N年可变成本
        totalCostExpense: new Array(years).fill(0) // 第N年总成本费用
    };
```

## 6.计算利润和利润分配表 
### 入参
```js
    const {
        operatingYears, // 项目运营年限
        hydrogenProduction, // 第N年制氢量
        hydrogenPriceNoTax, // 氢气售价不含税
        oxygenProduction, // 第N年制氧量
        oxygenPriceNoTax, // 氧气售价不含税
        gridElectricity, // 第N年上网电量
        gridElectricityPriceNoTax, // 脱硫煤电价不含税
        revenueItemVATRate, // 收入项增值税率
        cityConstructionAndMaintenanceTaxRate, // 城市建设维护税率
        educationSurchargeRate, // 教育费附加税率
        localEducationSurchargeRate, // 地方教育费附加税率
        totalEducationSurchargeRate, // 总教育费附加税率
        totalCostExpense, // 第N年总成本费用
        annualInterest, // 第N年本年付息
        depreciation, // 第N年折旧费
        incomeTaxRate, // 第N年所得税率
        statutorySurplusReserveFundRate, // 法定盈余公积金率
        shareholderDividendRatio, // 股东分红占比
        inputTax, // 第N年进项税
        projectTotalInvestment, // 项目总投资
        totalProjectInvestment, // 工程总投资 (新增参数)
        fixedAssetsNoTax // 固定资产不含税
    } = params;
```

### 出参
```js
      const results = {
        nonTaxOperatingIncome: new Array(years).fill(0), // 第N年不含税营业收入
        outputTax: new Array(years).fill(0), // 第N年销项税
        inputTaxEndingBalance: new Array(years).fill(0), // 第N年进项税期末余额
        inputTaxBeginningBalance: new Array(years).fill(0), // 第N年进项税期初余额
        currentPeriodInputTax: new Array(years).fill(0), // 第N年本期进项税额
        inputTaxDeduction: new Array(years).fill(0), // 第N年进项税抵扣额
        valueAddedTax: new Array(years).fill(0), // 第N年应缴税金
        inputTaxRemainingAmount: new Array(years).fill(0), // 第N年进项税留底金额
        cityConstructionTax: new Array(years).fill(0), // 第N年城建税
        totalEducationSurcharge: new Array(years).fill(0), // 第N年总教育费附加
        localEducationSurcharge: new Array(years).fill(0), // 第N年地方教育费附加
        salesTaxAndAdditions: new Array(years).fill(0), // 第N年销售税金及附加
        taxableSubsidy: new Array(years).fill(0), // 第N年补贴收入应税
        taxExemptSubsidy: new Array(years).fill(0), // 第N年补贴收入免税
        totalProfit: new Array(years).fill(0), // 第N年利润总额
        accumulatedTotalProfit: new Array(years).fill(0), // 第N年累计利润总额
        previousYearLossCompensation: new Array(years).fill(0), // 第N年弥补以前年度亏损
        taxableIncome: new Array(years).fill(0), // 第N年应纳税所得额
        incomeTax: new Array(years).fill(0), // 第N年所得税
        netProfit: new Array(years).fill(0), // 第N年净利润
        initialUndistributedProfit: new Array(years).fill(0), // 第N年期初未分配的利润
        statutoryReserveFund: new Array(years).fill(0), // 第N年提取法定盈余公积金
        profitAvailableForDistribution: new Array(years).fill(0), // 第N年可供投资者分配的利润
        payableProfit: new Array(years).fill(0), // 第N年应付利润
        undistributedProfit: new Array(years).fill(0), // 第N年未分配利润
        interestAndTaxProfit: new Array(years).fill(0), // 第N年息税前利润
        EBITDA: new Array(years).fill(0) // 第N年息税折旧摊销前利润
    };
```

## 7.计算还本付息计算表 
### 入参
```js
const {
        longTermLoan, // 长期借款
        loanRate, // 借款利率 - 从关键指标模块获取
        loanTerm, // 贷款期限
        operatingYears // 项目运营年限
    } = params;
```

### 出参
```js
      const results = {
        longTermLoan: new Array(years).fill(0), // 第N年长期借款
        beginningLoanBalance: new Array(years).fill(0), // 第N年年初借款余额
        annualRepayment: new Array(years).fill(0), // 第N年本年还本
        annualInterest: new Array(years).fill(0), // 第N年本年付息
        endingLoanBalance: new Array(years).fill(0), // 第N年期末借款余额
        currentRepaymentAndInterest: new Array(years).fill(0), // 第N年当期还本付息
        workingCapitalLoanRepayment: new Array(years).fill(0), // 第N年偿还流动资金借款本金
        shortTermLoanRepayment: new Array(years).fill(0), // 第N年偿还短期借款本金
        workingCapitalInterest: new Array(years).fill(0), // 第N年流动资金利息
        shortTermLoanInterest: new Array(years).fill(0), // 第N年短期借款利息
        shortTermLoan: new Array(years).fill(0), // 第N年短期借款
        loanPrincipalRepayment: new Array(years).fill(0), // 第N年偿还借款本金
        workingCapitalLoan: new Array(years).fill(0), // 第N年流动资金借款
        loanInterestRepayment: new Array(years).fill(0), // 第N年偿还借款利息
        totalLoanPayment: new Array(years).fill(0) // 第N年借款本息合计
    };
```

## 8.计算项目投资现金流量表 
### 入参
```js
    const {
        operatingYears, // 项目运营年限
        hydrogenProduction, // 第N年制氢量
        hydrogenPriceWithTax, // 氢气售价含增值税
        oxygenProduction, // 第N年制氧量
        oxygenPriceWithTax, // 氧气售价含增值税
        gridElectricity, // 第N年上网电量
        gridElectricityPriceWithTax, // 脱硫煤电价含增值税
        fixedAssetsNoTax, // 固定资产不含税
        salvageRate, // 残值率
        workingCapital, // 流动资金
        fixedAssetsStaticInvestment, // 固定资产静态投资
        maintenanceCost, // 第N年维修费
        laborCost, // 第N年工资福利及劳保统筹和住房基金
        insuranceCost, // 第N年保险费
        waterCost, // 第N年水费
        electricityCost, // 第N年电费
        materialsCost, // 第N年材料费
        transportationCost, // 第N年运输成本
        equipmentOverhaulReplacementCost, // 第N年设备大修更换费用
        landRentalFee, // 第N年土地租赁费
        povertyAlleviationFee, // 第N年扶贫规费支出
        landTaxFee, // 第N年土地税费
        otherCost, // 第N年其他费用
        waterResourceTax, // 第N年水资源税
        inputTax, // 第N年进项税
        salesTaxAndAdditions, // 第N年销售税金及附加
        valueAddedTax, // 第N年应缴税金
        annualRepayment, // 第N年本年还本
        incomeTaxRate, // 第N年所得税率
        interestAndTaxProfit, // 第N年息税前利润
        discountRate // 折现率
    } = params;
```

### 出参
```js
      const results = {
        operatingIncomeWithTax: new Array(years).fill(0), // 第N年含税营业收入
        subsidyIncome: new Array(years).fill(0), // 第N年补贴收入
        fixedAssetsResidualValue: new Array(years).fill(0), // 第N年回收固定资产余值
        workingCapitalRecovery: new Array(years).fill(0), // 第N年回收流动资金
        projectInvestmentCashInflow: new Array(years).fill(0), // 第N年项目投资现金流入
        constructionInvestment: new Array(years).fill(0), // 第N年建设投资
        workingCapitalInvestment: new Array(years).fill(0), // 第N年流动资金
        operatingCost: new Array(years).fill(0), // 第N年经营成本
        valueAddedTaxInputPayment: new Array(years).fill(0), // 第N年增值税进项税缴纳
        valueAddedTaxPayment: new Array(years).fill(0), // 第N年缴纳增值税
        projectInvestmentCashOutflow: new Array(years).fill(0), // 第N年项目投资现金流出
        netCashFlowBeforeIncomeTax: new Array(years).fill(0), // 第N年所得税前净现金流量
        accumulatedNetCashFlowBeforeIncomeTax: new Array(years).fill(0), // 第N年累计所得税前净现金流量
        adjustedIncomeTax: new Array(years).fill(0), // 第N年调整所得税
        netCashFlowAfterIncomeTax: new Array(years).fill(0), // 第N年所得税后净现金流量
        accumulatedNetCashFlowAfterIncomeTax: new Array(years).fill(0), // 第N年累计所得税后净现金流量
        dynamicPaybackPeriod_discountFactor: new Array(years).fill(0), // 第N年动态回收期_折现系数
        dynamicPaybackPeriod_presentValueBeforeTax: new Array(years).fill(0), // 第N年动态回收期_税前现值
        dynamicPaybackPeriod_presentValueAfterTax: new Array(years).fill(0), // 第N年动态回收期_税后现值
        dynamicPaybackPeriod_accumulatedNetCashFlowBeforeTax: new Array(years).fill(0), // 第N年动态回收期_累计所得税前净现金流量
        dynamicPaybackPeriod_accumulatedNetCashFlowAfterTax: new Array(years).fill(0), // 第N年动态回收期_累计所得税后净现金流量
        accountsReceivable: new Array(years).fill(0), // 第N年应收账款
        interestCoverageRatio: new Array(years).fill(0), // 第N年利息备付率
        debtCoverageRatio: new Array(years).fill(0) // 第N年偿债备付率
    };
```

## 9.计算资本金财务现金流量表
### 入参
```js
    const {
        operatingYears, // 项目运营年限
        equityCapital, // 资本金
        netProfit, // 第N年净利润
        depreciation, // 第N年折旧费
        amortization, // 第N年摊销费
        longTermLoan, // 第N年长期借款
        loanPrincipalRepayment, // 第N年偿还借款本金
        annualInterest, // 第N年本年付息
        shortTermLoanInterest, // 第N年短期借款利息
        workingCapitalInterest, // 第N年流动资金利息
        operatingIncomeWithTax, // 第N年含税营业收入
        subsidyIncome, // 第N年补贴收入
        operatingCost, // 第N年经营成本
        inputTaxPayment, // 第N年增值税进项税缴纳
        salesTaxAndSurcharge, // 第N年销售税金及附加
        incomeTax, // 第N年所得税
        vatPayable, // 第N年维持运营投资(缴纳增值税)
        fixedAssetsNoTax, // 固定资产不含税
        salvageRate, // 残值率
        workingCapital, // 流动资金
        payableProfit, // 第N年应付利润
        discountRate // 折现率
    } = params;
```

### 出参
```js
      const results = {
        // 直接引入其他表的数据
        operatingIncomeWithTax: operatingIncomeWithTax ? [...operatingIncomeWithTax] : new Array(years).fill(0), // 第N年含税营业收入
        subsidyIncome: subsidyIncome ? [...subsidyIncome] : new Array(years).fill(0), // 第N年补贴收入
        projectCapitalInvestment: new Array(years).fill(0), // 第N年项目资本金
        loanPrincipalRepayment: loanPrincipalRepayment ? [...loanPrincipalRepayment] : new Array(years).fill(0), // 第N年偿还借款本金
        annualInterest: annualInterest ? [...annualInterest] : new Array(years).fill(0), // 第N年本年付息
        shortTermLoanInterest: shortTermLoanInterest ? [...shortTermLoanInterest] : new Array(years).fill(0), // 第N年短期借款利息
        workingCapitalInterest: workingCapitalInterest ? [...workingCapitalInterest] : new Array(years).fill(0), // 第N年流动资金利息
        operatingCost: operatingCost ? [...operatingCost] : new Array(years).fill(0), // 第N年经营成本
        inputTaxPayment: inputTaxPayment ? [...inputTaxPayment] : new Array(years).fill(0), // 第N年增值税进项税缴纳
        salesTaxAndSurcharge: salesTaxAndSurcharge ? [...salesTaxAndSurcharge] : new Array(years).fill(0), // 第N年销售税金及附加
        incomeTax: incomeTax ? [...incomeTax] : new Array(years).fill(0), // 第N年所得税
        vatPayable: vatPayable ? [...vatPayable] : new Array(years).fill(0), // 第N年维持运营投资
        fixedAssetsResidualValue: new Array(years).fill(0), // 第N年回收固定资产余值
        workingCapitalRecovery: new Array(years).fill(0), // 第N年回收流动资金
        payableProfit: payableProfit ? [...payableProfit] : new Array(years).fill(0), // 第N年应付利润
        loanInterestRepayment: new Array(years).fill(0), // 第N年偿还借款利息
        
        // 需要计算的第一级子数据
        capitalCashInflow: new Array(years).fill(0), // 第N年资本金现金流入
        capitalCashOutflow: new Array(years).fill(0), // 第N年资本金现金流出
        capitalNetCashFlow: new Array(years).fill(0), // 第N年资本金税后净现金流量
        accumulatedNetCashFlow: new Array(years).fill(0), // 第N年资本金累计净现金流量
        capitalPretaxNetCashFlow: new Array(years).fill(0), // 第N年资本金税前净现金流量
        accumulatedPretaxNetCashFlow: new Array(years).fill(0), // 第N年资本金税前累计净现金流量
        discountFactor: new Array(years).fill(0), // 第N年折现系数
        presentValue: new Array(years).fill(0), // 第N年现值
        accumulatedPresentValue: new Array(years).fill(0) // 第N年累计现值
    };
```

## 10. 计算财务计划现金流量表 
### 入参
```js
    const {
        operatingYears, // 项目运营年限
        operatingIncomeWithTax, // 第N年含税营业收入 - 来自8项目投资现金流量表
        salesTax, // 第N年销项税 - 来自6利润和利润分配表
        taxableSubsidyIncome, // 第N年补贴收入应税 - 来自6利润和利润分配表
        operatingCost, // 第N年经营成本 - 来自8项目投资现金流量表
        inputTax, // 第N年进项税 - 来自5总成本费用表
        salesTaxAndSurcharge, // 第N年销售税金及附加 - 来自6利润和利润分配表
        vatPayable, // 第N年应缴税金 - 来自6利润和利润分配表
        incomeTax, // 第N年所得税 - 来自6利润和利润分配表
        fixedAssetsResidualValue, // 第N年回收固定资产余值 - 来自8项目投资现金流量表
        workingCapitalRecovery, // 第N年回收流动资金 - 来自8项目投资现金流量表
        fixedAssetsStaticInvestment, // 固定资产静态投资 - 来自3固定资产投资估算表
        workingCapital, // 流动资金 - 来自3固定资产投资估算表或4投资计划与资金筹措表
        fundRaising, // 资金筹措 - 来自4投资计划与资金筹措表
        equityCapital, // 资本金 - 来自2融资计划表
        bankLoan, // 银行借款 - 来自2融资计划表
        annualRepayment, // 第N年本年还本 - 来自7还本付息计算表
        annualInterest, // 第N年本年付息 - 来自7还本付息计算表
        payableProfit // 第N年应付利润 - 来自6利润和利润分配表
    } = params;
```

### 出参
```js
  const results = {
        // 经营活动现金流量相关
        operatingIncomeWithTax: ensureArray(operatingIncomeWithTax, years), // 第N年含税营业收入(营业收入) - 8项目投资现金流量表
        salesTax: ensureArray(salesTax, years), // 第N年销项税 - 6利润和利润分配表
        taxableSubsidyIncome: ensureArray(taxableSubsidyIncome, years), // 第N年补贴收入应税 - 6利润和利润分配表
        operatingActivityOtherInflow: new Array(years).fill(0), // 第N年经营活动其他流入
        operatingActivityCashInflow: new Array(years).fill(0), // 第N年经营活动现金流入
        operatingCost: ensureArray(operatingCost, years), // 第N年经营成本 - 8项目投资现金流量表
        inputTax: ensureArray(inputTax, years), // 第N年增值税进项税额 - 5总成本费用表
        salesTaxAndSurcharge: ensureArray(salesTaxAndSurcharge, years), // 第N年销售税金及附加 - 6利润和利润分配表
        vatPayable: ensureArray(vatPayable, years), // 第N年应缴税金(增值税) - 6利润和利润分配表
        incomeTax: ensureArray(incomeTax, years), // 第N年所得税 - 6利润和利润分配表
        operatingActivityOtherOutflow: new Array(years).fill(0), // 第N年经营活动其他流出
        operatingActivityCashOutflow: new Array(years).fill(0), // 第N年经营活动现金流出
        operatingActivityNetCashFlow: new Array(years).fill(0), // 第N年经营活动净现金流量
        
        // 投资活动现金流量相关
        investmentActivityCashInflow: new Array(years).fill(0), // 第N年投资活动现金流入
        fixedAssetsResidualValue: ensureArray(fixedAssetsResidualValue, years), // 第N年回收固定资产余值 - 8项目投资现金流量表
        workingCapitalRecovery: ensureArray(workingCapitalRecovery, years), // 第N年回收流动资金 - 8项目投资现金流量表
        investmentActivityBuildingInvestment: new Array(years).fill(0), // 第N年投资活动建设投资
        investmentActivityOperationInvestment: new Array(years).fill(0), // 第N年投资活动维持运营投资
        investmentActivityWorkingCapital: new Array(years).fill(0), // 第N年投资活动流动资金
        investmentActivityOtherOutflow: new Array(years).fill(0), // 第N年投资活动其他流出
        investmentActivityCashOutflow: new Array(years).fill(0), // 第N年投资活动现金流出
        investmentActivityNetCashFlow: new Array(years).fill(0), // 第N年投资活动净现金流量
        
        // 筹资活动现金流量相关
        financingActivityCashInflow: new Array(years).fill(0), // 第N年筹资活动现金流入
        equityCapitalInvestment: new Array(years).fill(0), // 第N年项目资本金投入
        buildingInvestmentLoan: new Array(years).fill(0), // 第N年建设投资借款
        workingCapitalLoan: new Array(years).fill(0), // 第N年流动资金借款
        bond: new Array(years).fill(0), // 第N年债券
        shortTermLoan: new Array(years).fill(0), // 第N年短期借款
        financingActivityOtherInflow: new Array(years).fill(0), // 第N年筹资活动其他流入
        interestExpense: new Array(years).fill(0), // 第N年各种利息支出
        debtPrincipalRepayment: new Array(years).fill(0), // 第N年偿还债务本金
        payableProfit: ensureArray(payableProfit, years), // 第N年应付利润 - 6利润和利润分配表
        financingActivityOtherOutflow: new Array(years).fill(0), // 第N年筹资活动其他流出
        financingActivityCashOutflow: new Array(years).fill(0), // 第N年筹资活动现金流出
        financingActivityNetCashFlow: new Array(years).fill(0), // 第N年筹资活动净现金流量
        
        // 汇总数据
        netCashFlow: new Array(years).fill(0), // 第N年财务计划净现金流量
        accumulatedSurplusFunds: new Array(years).fill(0) // 第N年财务计划累计盈余资金
    };
```

## 11. 计算资产负债表
### 入参
```js
    const {
        operatingYears, // 项目运营年限
        equityCapital, // 资本金
        endingCashBalance, // 第N年期末现金余额 (已废弃，使用财务计划累计盈余资金代替)
        accumulatedSurplusFunds, // 第N年财务计划累计盈余资金 - 从10财务计划现金流量表获取
        accountsReceivable, // 第N年应收账款
        fixedAssetsNoTax, // 固定资产不含税
        salvageRate, // 残值率
        depreciation, // 第N年折旧费
        amortizationCost, // 第N年摊销费
        payableProfit, // 第N年应付利润
        statutoryReserveFund, // 第N年提取法定盈余公积金
        netProfit, // 第N年净利润
        endingLoanBalance, // 第N年期末借款余额
        previousYearLossCompensation, // 第N年弥补以前年度亏损
        projectTotalInvestment, // 项目总投资
        workingCapital, // 流动资金
        shortTermLoan, // 第N年短期借款
        workingCapitalLoan, // 第N年流动资金借款
        vatDeductibleAssets, // 第N年可抵扣增值税形成的资产
        otherLiabilities, // 第N年负债其他
        inputTaxBeginningBalance // 第N年进项税期初余额
    } = params;
```

### 出参
```js
      const results = {
        // 资产部分
        totalCurrentAssets: new Array(years).fill(0), // 第N年流动资产总值
        accumulatedSurplusFunds: new Array(years).fill(0), // 第N年累计盈余资金
        currentAssets: new Array(years).fill(0), // 第N年流动资产
        constructionInProgress: new Array(years).fill(0), // 第N年在建工程
        netFixedAssets: new Array(years).fill(0), // 第N年固定资产净值
        netIntangibleAndOtherAssets: new Array(years).fill(0), // 第N年无形及其他资产净值
        vatDeductibleAssets: new Array(years).fill(0), // 第N年可抵扣增值税形成的资产
        assets: new Array(years).fill(0), // 第N年资产
        
        // 负债及所有者权益部分
        totalCurrentLiabilities: new Array(years).fill(0), // 第N年流动负债总额
        currentYearShortTermLoan: new Array(years).fill(0), // 第N年本年短期借款
        otherLiabilities: new Array(years).fill(0), // 第N年负债其他
        constructionInvestmentLoan: new Array(years).fill(0), // 第N年建设投资借款
        workingCapitalLoan: new Array(years).fill(0), // 第N年流动资金借款
        totalLiabilities: new Array(years).fill(0), // 第N年负债小计
        ownersEquity: new Array(years).fill(0), // 第N年所有者权益
        capital: new Array(years).fill(0), // 第N年资本金
        capitalReserve: new Array(years).fill(0), // 第N年资本公积
        accumulatedSurplusReserves: new Array(years).fill(0), // 第N年累计盈余公积金
        accumulatedUndistributedProfits: new Array(years).fill(0), // 第N年累计未分配利润
        totalLiabilitiesAndEquity: new Array(years).fill(0), // 第N年负债及所有者权益
        assetLiabilityRatio: new Array(years).fill(0) // 第N年资产负债率
    };
```

## 12. 计算财务指标汇总表 
### 入参
```js
const {
        operatingYears, // 项目运营年限
        discountRate, // 折现率
        projectTotalInvestment, // 项目总投资
        totalCostExpense, // 第N年总成本费用
        nonTaxOperatingIncome, // 第N年不含税营业收入
        netCashFlowBeforeIncomeTax, // 第N年所得税前净现金流量
        netCashFlowAfterIncomeTax, // 第N年所得税后净现金流量
        dynamicPaybackPeriod_presentValueBeforeTax, // 第N年动态回收期_税前现值
        dynamicPaybackPeriod_presentValueAfterTax, // 第N年动态回收期_税后现值
        dynamicPaybackPeriod_accumulatedNetCashFlowBeforeTax, // 第N年动态回收期_累计所得税前净现金流量
        dynamicPaybackPeriod_accumulatedNetCashFlowAfterTax, // 第N年动态回收期_累计所得税后净现金流量
        accumulatedNetCashFlowBeforeIncomeTax, // 第N年累计所得税前净现金流量
        accumulatedNetCashFlowAfterIncomeTax, // 第N年累计所得税后净现金流量
        capitalPretaxNetCashFlow, // 第N年资本金税前净现金流量
        capitalNetCashFlow, // 第N年资本金税后净现金流量
        presentValue, // 第N年现值
        accumulatedPresentValue, // 第N年累计现值
        equityCapital, // 资本金
        bankLoan, // 银行借款
        workingCapital, // 流动资金
        fixedAssetsStaticInvestment, // 固定资产静态投资
        constructionPeriodInterest, // 建设期利息
        hydrogenProduction, // 第N年制氢量
        installedCapacity, // 装机量
        interestAndTaxProfit, // 第N年息税前利润
        operatingCost, // 第N年经营成本
        salesTaxAndAdditions, // 第N年销售税金及附加
        totalProfit, // 第N年利润总额
        incomeTax, // 第N年所得税
        netProfit, // 第N年净利润
        vatPayable, // 第N年应缴税金
        vatSubsidyIncome, // 第N年补贴收入应税,
        gridElectricity, // 第N年上网电量
        greenHydrogenElectricity, // 第N年绿电制氢电量
        assetLiabilityRatio, // 第N年资产负债率
        hydrogenPriceWithTax, // 运营期氢气
        fixedPowerAssetsNoTax, //固定发电资产不含税
        powerDepreciation, // 第N年发电折旧费
        interestExpenseWithTax, // 第N年含税利息支出
        windTurbineOverhaulReplacementCost, // 第N年风电机组大修更换费用
        photovoltaicOverhaulReplacementCost, // 第N年光伏组件大修更换费用
        energyStorageOverhaulReplacementCost, // 第N年储能系统大修更换费用
        landRentalFee, // 第N年土地租赁费
        povertyAlleviationFee, // 第N年扶贫规费支出
        landTaxFee, // 第N年土地税费
        powerMaintenanceCost, // 第N年发电运维成本
        laborCost, // 第N年工资福利及劳保统筹和住房基金
        powerInsuranceCost, // 第N年保险费
        windTurbineMaterialsCost, // 第N年风电机组材料费
        photovoltaicMaterialsCost, // 第N年光伏组件材料费
        energyStorageMaterialsCost, // 第N年储能系统材料费
        powerOtherCost, // 第N年发电其他费用
        amortizationCost, // 第N年发电摊销费
        powerDiscountFactor, // 第N年发电折现系数
        salvageRate, // 第N年发电残值率
        discountedPowerGeneration, // 第N年发电量现值
        incomeTaxRate, // 所得税率
        depreciation, // 第N年发电折旧费
        fixedAssetsResidualValue, // 发电资产残值
        fixedAssetsNoTax, // 固定资产不含税
    } = params;
```

### 出参
```js
  const results = {
        // 基本信息
        installedCapacity: installCapacity, // 装机规模(MW)
        operatingYears: projectLife, // 项目经营期(年)
        
        // 发电量
        averageAnnualGeneration: 0, // 年均发电量(万kW·h)
        totalGridElectricity: 0, // 上网总结算电量(万kW·h)
        averageAnnualGridElectricity: 0, // 年均上网结算电量(万kW·h)
        annualEffectiveUtilizationHours: 0, // 年有效利用小时数(小时)
        
        // 投资指标
        projectTotalInvestment: totalInvestment, // 项目总投资(万元)
        fixedAssetsStaticInvestment: staticInvestment, // 固定资产静态投资(万元)
        constructionPeriodInterest: interest, // 建设期利息(万元)
        workingCapital: working, // 流动资金(万元)
        staticInvestmentPerWatt: 0, // 单瓦静态总投资(元/W)
        dynamicInvestmentPerWatt: 0, // 单瓦动态总投资(元/W)
        investmentCostPerKWh: 0, // 度电投资成本(元/kWh)
        
        // 资金来源
        equityCapitalAmount: capital, // 项目资本金投入金额(万元)
        totalBankLoanAmount: loan, // 项目银行贷款总额(万元)
        
        // 经营指标
        totalSalesRevenue: 0, // 销售收入总额(万元)
        totalCostExpense: 0, // 总成本费用(万元)
        averageAnnualOperatingCost: 0, // 年平均运营成本(万元)
        totalGenerationCostPerUnit: 0, // 单位发电总成本(元/kWh)
        operatingCostPerKWh: 0, // 度电运营成本(元/kWh)
        totalSalesTaxSurcharges: 0, // 销售税金附加总额(万元)
        totalPowerGenerationProfit: 0, // 发电利润总额(万元)
        levelizedCostOfElectricity: 0, // 平准化度电成本LCOE(元/kWh)
        
        // 回收期指标
        paybackPeriodAfterTax_static: 0, // 税后投资回收期(静态)(年)
        paybackPeriodAfterTax_dynamic: 0, // 税后投资回收期(动态)(年)
        
        // 收益率指标
        projectInvestmentFIRR_beforeTax: 0, // 项目投资财务内部收益率_税前(%)
        projectInvestmentFIRR_afterTax: 0, // 项目投资财务内部收益率_税后(%)
        equityCapitalFIRR_beforeTax: 0, // 资本金财务内部收益率_税前(%)
        equityCapitalFIRR_afterTax: 0, // 资本金财务内部收益率_税后(%)
        
        // 其他财务指标
        hydrogenPrice: ensureNumber(hydrogenPriceWithTax), // 氢气价格(元/kg)
        projectInvestmentNPV_beforeTax: 0, // 项目投资财务税前净现值(万元)
        equityCapitalNPV_afterTax: 0, // 资本金财务税后净现值(万元)
        returnOnInvestment: 0, // 总投资收益率ROI(%)
        investmentTaxRate: 0, // 投资利税率(%)
        returnOnEquity: 0, // 项目资本金净利润率ROE(%)
        assetLiabilityRatio: 0, // 资产负债率(%)
        vatRefund50Percent: 0, // 增值税即征即退50%(万元)
        lcoe: 0,
        lcoh: 0,
        lcoh_ori: 0,
    };
```

## 13. 计算年度制氢及上网电量 
### 入参
```js
    const {
        installedCapacity, // 装机量
        firstYearPowerGenerationHour, // 首年发电小时数
        operatingYears, // 项目运营年限
        yearTwoDegradationPercentage, // 第2年衰减百分值
        yearNDegradationPercentage, // 第N年衰减百分值
        powerToHydrogenRatio, // 发电制氢比例
        discountRate, // 折现系数
        powerGenerationCostSensitivityFactor, // 发电成本敏感系数
        electrolyzerPowerConsumption, // 电解槽电耗

        firstYearDownGridPower, // 首年下网电量
        genH2ConsumeWater, // 制氢耗水量(L/Nm³)
        config,
    } = params;
```

### 出参
```js
     const results = {
        yearDegradationPercentage: new Array(years).fill(0), // 第N年衰减百分值
        firstYearPowerGenerationHour: firstYearPowerGenerationHour, // 首年发电小时数
        componentDegradationEfficiency: new Array(years).fill(0), // 第N年组件衰减效率
        powerLimitationRate: new Array(years).fill(0), // 第N年限电率
        powerGenerationRate: new Array(years).fill(0), // 第N年发电率
        predictedPowerGenerationAfterDegradation: new Array(years).fill(0), // 第N年预计发电量衰减后(单位：万度)
        predictedPowerGenerationWithPowerLimitation: new Array(years).fill(0), // 第N年预计发电量衰减加限电(单位：万度)
        gridElectricity: new Array(years).fill(0), // 第N年上网电量
        greenHydrogenElectricity: new Array(years).fill(0), // 第N年绿电制氢电量
        powerDiscountFactor: new Array(years).fill(0), // 第N年发电折现系数
        discountedPowerGeneration: new Array(years).fill(0), // 第N年发电量折现值
        gridHydrogenElectricity: new Array(years).fill(0), // 第N年下网制氢电量
        hydrogenProduction: new Array(years).fill(0), // 第N年制氢量(万公斤)
        oxygenProduction: new Array(years).fill(0), // 第N年制氧量(万公斤)
        waterConsumption: new Array(years).fill(0), // 第N年耗水量(万吨)
        greenPowerRatioAfterYears: new Array(years).fill(0)
    }; 
```