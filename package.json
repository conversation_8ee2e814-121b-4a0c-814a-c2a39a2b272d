{"name": "finance-model", "version": "1.0.0", "description": "财务测算模型", "main": "finance_model/index.js", "scripts": {"build": "npm run build:webpack && npm run optimize", "build:webpack": "cross-env NODE_ENV=production webpack --config build/webpack.config.js --mode production", "optimize": "node build/optimize.js", "build:dev": "webpack --config build/webpack.config.js --mode development", "test": "node test/test.js", "start:server": "node server/index.js", "start:server:dev": "nodemon server/index.js", "test:api": "node server/test-api.js", "example": "node server/example-input.js"}, "dependencies": {"dayjs": "^1.11.13", "lodash": "^4.17.21", "mathjs": "^14.5.1", "express": "^4.18.2", "cors": "^2.8.5", "axios": "^1.6.0"}, "devDependencies": {"@babel/core": "^7.23.6", "@babel/preset-env": "^7.23.6", "@babel/plugin-transform-runtime": "^7.23.6", "@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-terser": "^0.4.4", "babel-loader": "^9.1.3", "cross-env": "^7.0.3", "glob": "^10.3.10", "rollup": "^4.9.6", "terser": "^5.43.1", "webpack": "^5.89.0", "webpack-bundle-analyzer": "^4.10.1", "webpack-cli": "^5.1.4", "nodemon": "^3.0.0", "@types/node": "^20.0.0", "typescript": "^5.0.0"}, "files": ["dist/", "finance_model/", "README.md"], "keywords": ["finance", "model", "calculation", "hydrogen", "renewable-energy"], "author": "", "license": "MIT"}