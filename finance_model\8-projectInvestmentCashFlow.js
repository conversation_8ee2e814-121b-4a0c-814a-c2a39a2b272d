/**
 * 计算项目投资现金流量表
 * @param {Object} params - 输入参数
 * @param {boolean} showLogs - 是否显示表格数据（可选，默认为false）
 * @returns {Object} - 项目投资现金流量表结果
 */
function calculateProjectInvestmentCashFlow(params, showLogs = false) {
    const {
        operatingYears, // 项目运营年限
        hydrogenProduction, // 第N年制氢量
        hydrogenPriceWithTax, // 氢气售价含增值税
        oxygenProduction, // 第N年制氧量
        oxygenPriceWithTax, // 氧气售价含增值税
        gridElectricity, // 第N年上网电量
        gridElectricityPriceWithTax, // 脱硫煤电价含增值税
        fixedAssetsNoTax, // 固定资产不含税
        salvageRate, // 残值率
        workingCapital, // 流动资金
        fixedAssetsStaticInvestment, // 固定资产静态投资
        maintenanceCost, // 第N年维修费
        laborCost, // 第N年工资福利及劳保统筹和住房基金
        insuranceCost, // 第N年保险费
        waterCost, // 第N年水费
        electricityCost, // 第N年电费
        materialsCost, // 第N年材料费
        transportationCost, // 第N年运输成本
        equipmentOverhaulReplacementCost, // 第N年设备大修更换费用
        landRentalFee, // 第N年土地租赁费
        povertyAlleviationFee, // 第N年扶贫规费支出
        landTaxFee, // 第N年土地税费
        otherCost, // 第N年其他费用
        waterResourceTax, // 第N年水资源税
        inputTax, // 第N年进项税
        salesTaxAndAdditions, // 第N年销售税金及附加
        valueAddedTax, // 第N年应缴税金
        annualRepayment, // 第N年本年还本
        incomeTaxRate, // 第N年所得税率
        interestAndTaxProfit, // 第N年息税前利润
        discountRate // 折现率
    } = params;

    // 创建结果对象
    const years = operatingYears + 1; // +1 是因为包括第1年(建设期)
    const results = {
        operatingIncomeWithTax: new Array(years).fill(0), // 第N年含税营业收入
        subsidyIncome: new Array(years).fill(0), // 第N年补贴收入
        fixedAssetsResidualValue: new Array(years).fill(0), // 第N年回收固定资产余值
        workingCapitalRecovery: new Array(years).fill(0), // 第N年回收流动资金
        projectInvestmentCashInflow: new Array(years).fill(0), // 第N年项目投资现金流入
        constructionInvestment: new Array(years).fill(0), // 第N年建设投资
        workingCapitalInvestment: new Array(years).fill(0), // 第N年流动资金
        operatingCost: new Array(years).fill(0), // 第N年经营成本
        valueAddedTaxInputPayment: new Array(years).fill(0), // 第N年增值税进项税缴纳
        valueAddedTaxPayment: new Array(years).fill(0), // 第N年缴纳增值税
        projectInvestmentCashOutflow: new Array(years).fill(0), // 第N年项目投资现金流出
        netCashFlowBeforeIncomeTax: new Array(years).fill(0), // 第N年所得税前净现金流量
        accumulatedNetCashFlowBeforeIncomeTax: new Array(years).fill(0), // 第N年累计所得税前净现金流量
        adjustedIncomeTax: new Array(years).fill(0), // 第N年调整所得税
        netCashFlowAfterIncomeTax: new Array(years).fill(0), // 第N年所得税后净现金流量
        accumulatedNetCashFlowAfterIncomeTax: new Array(years).fill(0), // 第N年累计所得税后净现金流量
        dynamicPaybackPeriod_discountFactor: new Array(years).fill(0), // 第N年动态回收期_折现系数
        dynamicPaybackPeriod_presentValueBeforeTax: new Array(years).fill(0), // 第N年动态回收期_税前现值
        dynamicPaybackPeriod_presentValueAfterTax: new Array(years).fill(0), // 第N年动态回收期_税后现值
        dynamicPaybackPeriod_accumulatedNetCashFlowBeforeTax: new Array(years).fill(0), // 第N年动态回收期_累计所得税前净现金流量
        dynamicPaybackPeriod_accumulatedNetCashFlowAfterTax: new Array(years).fill(0), // 第N年动态回收期_累计所得税后净现金流量
        accountsReceivable: new Array(years).fill(0), // 第N年应收账款
        interestCoverageRatio: new Array(years).fill(0), // 第N年利息备付率
        debtCoverageRatio: new Array(years).fill(0) // 第N年偿债备付率
    };

    // 计算各年数据
    for (let i = 0; i < years; i++) {
        // 第N年含税营业收入
        if (i === 0) {
            results.operatingIncomeWithTax[i] = 0; // 第1年含税营业收入=0
        } else {
            // 第N年含税营业收入 = 第N年制氢量 * 氢气售价含增值税 + 第N年制氧量 * 氧气售价含增值税 + 脱硫煤电价含增值税 * 第N年上网电量
            results.operatingIncomeWithTax[i] = 
                hydrogenProduction[i] * hydrogenPriceWithTax + 
                oxygenProduction[i] * oxygenPriceWithTax + 
                gridElectricityPriceWithTax * gridElectricity[i];
        }
        
        // 第N年补贴收入
        results.subsidyIncome[i] = 0; // 默认值为0
        
        // 第N年回收固定资产余值
        if (i < operatingYears) {
            results.fixedAssetsResidualValue[i] = 0; // 第N(N<=项目运营年限)年回收固定资产余值=0
        } else {
            // 第N(N=项目运营年限+1)年回收固定资产余值 = 固定资产不含税 * 残值率
            results.fixedAssetsResidualValue[i] = fixedAssetsNoTax * salvageRate;
        }
        
        // 第N年回收流动资金
        if (i < operatingYears) {
            results.workingCapitalRecovery[i] = 0; // 第N(N<=项目运营年限)年回收流动资金=0
        } else {
            // 第N(N=项目运营年限+1)年回收流动资金 = 流动资金
            results.workingCapitalRecovery[i] = workingCapital;
        }
        
        // 第N年项目投资现金流入
        if (i === 0) {
            results.projectInvestmentCashInflow[i] = 0; // 第1年项目投资现金流入=0
        } else {
            // 第N年项目投资现金流入 = 第N年含税营业收入 + 第N年补贴收入 + 第N年回收固定资产余值 + 第N年回收流动资金
            results.projectInvestmentCashInflow[i] = 
                results.operatingIncomeWithTax[i] + 
                results.subsidyIncome[i] + 
                results.fixedAssetsResidualValue[i] + 
                results.workingCapitalRecovery[i];
        }
        
        // 第N年建设投资
        if (i === 0) {
            // 第1年建设投资 = 固定资产静态投资
            results.constructionInvestment[i] = fixedAssetsStaticInvestment;
        } else {
            results.constructionInvestment[i] = 0; // 第N(N>=2)年建设投资=0
        }
        
        // 第N年流动资金
        if (i === 0) {
            // 第1年流动资金 = 流动资金
            results.workingCapitalInvestment[i] = workingCapital;
        } else {
            results.workingCapitalInvestment[i] = 0; // 第N(N>=2)年流动资金=0
        }
        
        // 第N年经营成本
        if (i === 0) {
            results.operatingCost[i] = 0; // 第1年经营成本=0
        } else {
            // 第N年经营成本 = 第N年维修费
            // + 第N年工资福利及劳保统筹和住房基金
            // + 第N年保险费
            // + 第N年水费
            // + 第N年电费
            // + 第N年材料费
            // + 第N年运输成本
            // + 第N年设备大修更换费用
            // + 第N年土地租赁费
            // + 第N年扶贫规费支出
            // + 第N年土地税费包含耕地占用税及土地使用税
            // + 第N年其他费用
            // + 第N年水资源税
            results.operatingCost[i] = 
                maintenanceCost[i] + 
                laborCost[i] + 
                insuranceCost[i] + 
                waterCost[i] + 
                electricityCost[i] + 
                materialsCost[i] + 
                transportationCost[i] + 
                equipmentOverhaulReplacementCost[i] + 
                landRentalFee[i] + 
                povertyAlleviationFee[i] + 
                landTaxFee[i] + 
                otherCost[i] + 
                waterResourceTax[i];
        }
        
        // 第N年增值税进项税缴纳
        if (i === 0) {
            results.valueAddedTaxInputPayment[i] = 0; // 第1年增值税进项税缴纳=0
        } else {
            // 第N(N>=2)年增值税进项税缴纳 = 第N年进项税
            results.valueAddedTaxInputPayment[i] = inputTax[i];
        }
        
        // 第N年缴纳增值税
        if (i === 0) {
            results.valueAddedTaxPayment[i] = 0; // 第1年缴纳增值税=0
        } else {
            // 第N年缴纳增值税 = 第N年应缴税金
            results.valueAddedTaxPayment[i] = valueAddedTax[i];
        }
        
        // 第N年项目投资现金流出 = 第N年建设投资 + 第N年流动资金 + 第N年经营成本 + 第N年增值税进项税缴纳 + 第N年销售税金及附加 + 第N年缴纳增值税
        results.projectInvestmentCashOutflow[i] = 
            results.constructionInvestment[i] + 
            results.workingCapitalInvestment[i] + 
            results.operatingCost[i] + 
            results.valueAddedTaxInputPayment[i] + 
            salesTaxAndAdditions[i] + 
            results.valueAddedTaxPayment[i];
        
        // 第N年所得税前净现金流量 = 第N年项目投资现金流入 - 第N年项目投资现金流出
        results.netCashFlowBeforeIncomeTax[i] = 
            results.projectInvestmentCashInflow[i] - results.projectInvestmentCashOutflow[i];
        
        // 第N年累计所得税前净现金流量
        if (i === 0) {
            // 第1年累计所得税前净现金流量 = 第1年所得税前净现金流量
            results.accumulatedNetCashFlowBeforeIncomeTax[i] = results.netCashFlowBeforeIncomeTax[i];
        } else {
            // 第N(N>=2)年累计所得税前净现金流量 = 第N年所得税前净现金流量 + 第N-1年累计所得税前净现金流量
            results.accumulatedNetCashFlowBeforeIncomeTax[i] = 
                results.netCashFlowBeforeIncomeTax[i] + results.accumulatedNetCashFlowBeforeIncomeTax[i-1];
        }
        
        // 第N年调整所得税
        if (i === 0) {
            // 第1年调整所得税 = 第1年息税前利润 * 所得税率
            results.adjustedIncomeTax[i] = interestAndTaxProfit[i] * incomeTaxRate[i];
        } else if (i >= 1 && i <= 3) {
            // 第N(2<=N<=4)年调整所得税 = 第N年息税前利润 > 0 ? 第N年息税前利润 * 所得税率 * 0 : 0
            results.adjustedIncomeTax[i] = interestAndTaxProfit[i] > 0 ? 
                                      interestAndTaxProfit[i] * incomeTaxRate[i] * 0 : 0;
        } else {
            // 第N(N>=5)年调整所得税 = 第N年息税前利润 > 0 ? 第N年息税前利润 * 所得税率 * 0.5 : 0
            results.adjustedIncomeTax[i] = interestAndTaxProfit[i] > 0 ? 
                                      interestAndTaxProfit[i] * incomeTaxRate[i] * 0.5 : 0;
        }
        
        // 第N年所得税后净现金流量 = 第N年所得税前净现金流量 - 第N年调整所得税
        results.netCashFlowAfterIncomeTax[i] = 
            results.netCashFlowBeforeIncomeTax[i] - results.adjustedIncomeTax[i];
        
        // 第N年累计所得税后净现金流量
        if (i === 0) {
            // 第1年累计所得税后净现金流量 = 第1年所得税后净现金流量
            results.accumulatedNetCashFlowAfterIncomeTax[i] = results.netCashFlowAfterIncomeTax[i];
        } else {
            // 第N年累计所得税后净现金流量 = 第N年所得税后净现金流量 + 第N-1年累计所得税后净现金流量
            results.accumulatedNetCashFlowAfterIncomeTax[i] = 
                results.netCashFlowAfterIncomeTax[i] + results.accumulatedNetCashFlowAfterIncomeTax[i-1];
        }
        
        // 第N年动态回收期_折现系数
        if (i === 0) {
            results.dynamicPaybackPeriod_discountFactor[i] = 0; // 第1年动态回收期_折现系数=0
        } else {
            // 第N年动态回收期_折现系数 = 1/((1+折现率)^N)
            results.dynamicPaybackPeriod_discountFactor[i] = 1 / Math.pow(1 + discountRate, i + 1);
        }
        
        // 第N年动态回收期_税前现值
        if (i === 0) {
            results.dynamicPaybackPeriod_presentValueBeforeTax[i] = 0; // 第1年动态回收期_税前现值=0
        } else {
            // 第N年动态回收期_税前现值 = 第N年所得税前净现金流量 * 第N年动态回收期_折现系数
            results.dynamicPaybackPeriod_presentValueBeforeTax[i] = 
                results.netCashFlowBeforeIncomeTax[i] * results.dynamicPaybackPeriod_discountFactor[i];
        }
        
        // 第N年动态回收期_税后现值
        if (i === 0) {
            results.dynamicPaybackPeriod_presentValueAfterTax[i] = 0; // 第1年动态回收期_税后现值=0
        } else {
            // 第N年动态回收期_税后现值 = 第N年所得税后净现金流量 * 第N年动态回收期_折现系数
            results.dynamicPaybackPeriod_presentValueAfterTax[i] = 
                results.netCashFlowAfterIncomeTax[i] * results.dynamicPaybackPeriod_discountFactor[i];
        }
        
        // 第N年动态回收期_累计所得税前净现金流量
        if (i === 0) {
            // 第1年动态回收期_累计所得税前净现金流量 = 第1年累计所得税前净现金流量
            results.dynamicPaybackPeriod_accumulatedNetCashFlowBeforeTax[i] = 
                results.accumulatedNetCashFlowBeforeIncomeTax[i];
        } else if (i === 1) {
            // 第2年动态回收期_累计所得税前净现金流量 = 第1年累计所得税前净现金流量 + 第2年动态回收期_税前现值
            results.dynamicPaybackPeriod_accumulatedNetCashFlowBeforeTax[i] = 
                results.accumulatedNetCashFlowBeforeIncomeTax[0] + 
                results.dynamicPaybackPeriod_presentValueBeforeTax[i];
        } else {
            // 第N年动态回收期_累计所得税前净现金流量 = 第N-1年动态回收期_累计所得税前净现金流量 + 第N年动态回收期_税前现值
            results.dynamicPaybackPeriod_accumulatedNetCashFlowBeforeTax[i] = 
                results.dynamicPaybackPeriod_accumulatedNetCashFlowBeforeTax[i-1] + 
                results.dynamicPaybackPeriod_presentValueBeforeTax[i];
        }
        
        // 第N年动态回收期_累计所得税后净现金流量
        if (i === 0) {
            // 第1年动态回收期_累计所得税后净现金流量 = 第1年累计所得税后净现金流量
            results.dynamicPaybackPeriod_accumulatedNetCashFlowAfterTax[i] = 
                results.accumulatedNetCashFlowAfterIncomeTax[i];
        } else if (i === 1) {
            // 第2年动态回收期_累计所得税后净现金流量 = 第1年累计所得税后净现金流量 + 第2年动态回收期_税后现值
            results.dynamicPaybackPeriod_accumulatedNetCashFlowAfterTax[i] = 
                results.accumulatedNetCashFlowAfterIncomeTax[0] + 
                results.dynamicPaybackPeriod_presentValueAfterTax[i];
        } else {
            // 第N年动态回收期_累计所得税后净现金流量 = 第N-1年动态回收期_累计所得税后净现金流量 + 第N年动态回收期_税后现值
            results.dynamicPaybackPeriod_accumulatedNetCashFlowAfterTax[i] = 
                results.dynamicPaybackPeriod_accumulatedNetCashFlowAfterTax[i-1] + 
                results.dynamicPaybackPeriod_presentValueAfterTax[i];
        }
        
        // 第N年应收账款
        results.accountsReceivable[i] = 0; // 默认值为0
        
        // 第N年利息备付率
        if (i === 0) {
            // 第1年利息备付率 = 第1年本年付息 <= 0 ? 0 : 第1年息税前利润 / 第1年本年付息
            results.interestCoverageRatio[i] = annualRepayment[i] <= 0 ? 
                                         0 : interestAndTaxProfit[i] / annualRepayment[i];
        } else {
            // 第N年利息备付率 = 第N年息税前利润 / 第N年本年付息
            results.interestCoverageRatio[i] = annualRepayment[i] <= 0 ? 
                                         0 : interestAndTaxProfit[i] / annualRepayment[i];
        }
        
        // 第N年偿债备付率 = 第N年所得税后净现金流量 <= 0 ? 0 : 第N年所得税后净现金流量 / 第N年借款本息合计
        const totalLoanPayment = annualRepayment[i]; // 可能需要更完整的借款本息合计计算
        results.debtCoverageRatio[i] = results.netCashFlowAfterIncomeTax[i] <= 0 ? 
                                  0 : results.netCashFlowAfterIncomeTax[i] / totalLoanPayment;
    }

    // 只有在showLogs为true时才打印表格
    if (showLogs) {
        console.log("\n===== 项目投资现金流量表 =====");
        
        // 创建包含结果和中文标题的数组
        const cashInflowResults = [];
        const cashOutflowResults = [];
        const netCashFlowResults = [];
        const cumulativeResults = [];
        
        // 为每一年填充数据
        for (let i = 0; i < years; i++) {
            const yearData = {
                "年份": `第${i+1}年`
            };
            
            // 现金流入
            cashInflowResults.push({
                ...yearData,
                "含税营业收入": results.operatingIncomeWithTax[i],
                "补贴收入": results.subsidyIncome[i],
                "回收固定资产余值": results.fixedAssetsResidualValue[i],
                "回收流动资金": results.workingCapitalRecovery[i],
                "项目投资现金流入": results.projectInvestmentCashInflow[i]
            });
            
            // 现金流出
            cashOutflowResults.push({
                ...yearData,
                "建设投资": results.constructionInvestment[i],
                "流动资金": results.workingCapitalInvestment[i],
                "经营成本": results.operatingCost[i],
                "增值税进项税缴纳": results.valueAddedTaxInputPayment[i],
                "销售税金及附加": salesTaxAndAdditions[i],
                "缴纳增值税": results.valueAddedTaxPayment[i],
                "项目投资现金流出": results.projectInvestmentCashOutflow[i]
            });
            
            // 净现金流量
            netCashFlowResults.push({
                ...yearData,
                "所得税前净现金流量": results.netCashFlowBeforeIncomeTax[i],
                "累计所得税前净现金流量": results.accumulatedNetCashFlowBeforeIncomeTax[i],
                "调整所得税": results.adjustedIncomeTax[i]
            });
            
            // 累计和指标
            cumulativeResults.push({
                ...yearData,
                "所得税后净现金流量": results.netCashFlowAfterIncomeTax[i],
                "累计所得税后净现金流量": results.accumulatedNetCashFlowAfterIncomeTax[i]
            });
        }
        
        // 显示表格
        console.log("1. 现金流入情况");
        console.table(cashInflowResults);
        
        console.log("\n2. 现金流出情况");
        console.table(cashOutflowResults);
        
        console.log("\n3. 税前净现金流量情况");
        console.table(netCashFlowResults);
        
        console.log("\n4. 税后净现金流量情况");
        console.table(cumulativeResults);
        
    }

    return results;
}

module.exports = { calculateProjectInvestmentCashFlow }; 