/**
 * 计算固定资产投资估算
 * @param {Object} params - 输入参数
 * @param {boolean} showLogs - 是否显示表格数据（可选，默认为false）
 * @returns {Object} - 固定资产投资估算结果
 */
function calculateFixedAssetsInvestmentEstimation(params, showLogs = false) {
    const {
        // 拆分后的风机参数
        windTurbineEquipment,   // 风机设备
        windTurbineInstallation, // 风机安装
        windTurbineBuilding,    // 风机建筑
        windTurbineOthers,      // 风机其他
        
        // 拆分后的光伏参数
        photovoltaicEquipment,   // 光伏设备
        photovoltaicInstallation, // 光伏安装
        photovoltaicBuilding,    // 光伏建筑
        photovoltaicOthers,      // 光伏其他
        
        // 拆分后的储能参数
        energyStorageEquipment,   // 储能设备
        energyStorageInstallation, // 储能安装
        energyStorageBuilding,    // 储能建筑
        energyStorageOthers,      // 储能其他
        
        // 拆分后的电解槽参数
        electrolyzerEquipment,   // 电解槽设备
        electrolyzerInstallation, // 电解槽安装
        electrolyzerBuilding,    // 电解槽建筑
        electrolyzerOthers,      // 电解槽其他
        
        // 拆分后的储罐参数
        hydrogenStorageEquipment,   // 储罐设备
        hydrogenStorageInstallation, // 储罐安装
        hydrogenStorageBuilding,    // 储罐建筑
        hydrogenStorageOthers,      // 储罐其他
        
        // 其他参数
        projectChangeFactor, // 项目总概算变化系数
        fixedAssetsFinancialFactor, // 财务测算固定资产敏感系数
        VATRate, // 成本项电费增值税
        installationTaxRate, // 安装税
        installedCapacity, // 装机量
        loanRate, // 借贷利率
        financingRatioBase = 0.7 // 融资比例基准，默认70%
    } = params;

    // 计算发电侧总设备及安装
    const totalPowerEquipment = windTurbineEquipment + photovoltaicEquipment + energyStorageEquipment;
    const totalPowerInstallation = windTurbineInstallation + photovoltaicInstallation + energyStorageInstallation;
    const totalPowerBuilding = windTurbineBuilding + photovoltaicBuilding + energyStorageBuilding;
    const totalPowerOthers = windTurbineOthers + photovoltaicOthers + energyStorageOthers;
    
    // 计算制氢侧总设备及安装
    const totalHydrogenEquipment = electrolyzerEquipment + hydrogenStorageEquipment;
    const totalHydrogenInstallation = electrolyzerInstallation + hydrogenStorageInstallation;
    const totalHydrogenBuilding = electrolyzerBuilding + hydrogenStorageBuilding;
    const totalHydrogenOthers = electrolyzerOthers + hydrogenStorageOthers;

    // 计算设备及安装工程合计 = 设备购置费 + 安装工程费
    const equipmentPurchaseCost = (totalPowerEquipment + totalHydrogenEquipment) * projectChangeFactor;
    const installationCost = (totalPowerInstallation + totalHydrogenInstallation) * projectChangeFactor;
    const equipmentAndInstallationTotal = (equipmentPurchaseCost + installationCost) * fixedAssetsFinancialFactor;

    // 计算机电设备及安装工程 = 设备及安装工程合计 * 财务测算固定资产敏感系数
    const mechanicalAndElectricalEquipment = equipmentAndInstallationTotal;

    // 计算建筑工程 = 土建工程 * 财务测算固定资产敏感系数
    const civilEngineeringWorks = (totalPowerBuilding + totalHydrogenBuilding) * projectChangeFactor;
    const constructionWorks = civilEngineeringWorks * fixedAssetsFinancialFactor;

    // 计算其他费用 = 其他费用合计 * 财务测算固定资产敏感系数
    const otherExpenseTotal = (totalPowerOthers + totalHydrogenOthers) * projectChangeFactor;
    const otherExpenses = otherExpenseTotal * fixedAssetsFinancialFactor;

    // 计算流动资金 = 装机量 * 30 / 10
    const workingCapital = installedCapacity * 30 / 10;

    // 计算固定资产静态投资 = 机电设备及安装工程 + 建筑工程 + 其他费用
    const fixedAssetsStaticInvestment = mechanicalAndElectricalEquipment + constructionWorks + otherExpenses;

    // 计算建设期利息 = 建设期内资利息 (按照经评测算文档计算)
    // 值1 = 固定资产静态投资 + 流动资金
    const value1 = fixedAssetsStaticInvestment + workingCapital;
    // 值2 = 借贷利率 * 融资比例基准
    const value2 = loanRate * financingRatioBase;
    // 建设期内资利息 = 值1/(2/值2-1)
    const constructionPeriodInterest = value1 / (2 / value2 - 1);

    // 计算项目总投资 = 固定资产静态投资 + 建设期利息 + 流动资金
    let projectTotalInvestment = fixedAssetsStaticInvestment + constructionPeriodInterest + workingCapital;

    // === 计算各组件不含税值 ===
    
    // 风机设备及安装不含税
    const windTurbineEquipmentNoTax = (windTurbineEquipment * projectChangeFactor) / (1 + VATRate);
    const windTurbineInstallationNoTax = (windTurbineInstallation * projectChangeFactor) / (1 + installationTaxRate);
    const windTurbineEquipmentAndInstallationNoTax = windTurbineEquipmentNoTax + windTurbineInstallationNoTax;
    
    // 风机建筑不含税
    const windTurbineBuildingNoTax = (windTurbineBuilding * projectChangeFactor) / (1 + installationTaxRate);
    
    // 风机其他不含税
    const windTurbineOthersNoTax = windTurbineOthers * projectChangeFactor;
    
    // 风机利息不含税 - 固定为0
    const windTurbineInterestNoTax = 0;
    
    // 固定风机资产不含税
    const fixedWindTurbineAssetsNoTax = windTurbineEquipmentAndInstallationNoTax + 
                                     windTurbineBuildingNoTax + 
                                     windTurbineOthersNoTax + 
                                     windTurbineInterestNoTax;
    
    // 光伏设备及安装不含税
    const photovoltaicEquipmentNoTax = (photovoltaicEquipment * projectChangeFactor) / (1 + VATRate);
    const photovoltaicInstallationNoTax = (photovoltaicInstallation * projectChangeFactor) / (1 + installationTaxRate);
    const photovoltaicEquipmentAndInstallationNoTax = photovoltaicEquipmentNoTax + photovoltaicInstallationNoTax;
    
    // 光伏建筑不含税
    const photovoltaicBuildingNoTax = (photovoltaicBuilding * projectChangeFactor) / (1 + installationTaxRate);
    
    // 光伏其他不含税
    const photovoltaicOthersNoTax = photovoltaicOthers * projectChangeFactor;
    
    // 光伏利息不含税 - 固定为0
    const photovoltaicInterestNoTax = 0;
    
    // 固定光伏资产不含税
    const fixedPhotovoltaicAssetsNoTax = photovoltaicEquipmentAndInstallationNoTax + 
                                       photovoltaicBuildingNoTax + 
                                       photovoltaicOthersNoTax + 
                                       photovoltaicInterestNoTax;
    
    // 储能设备及安装不含税
    const energyStorageEquipmentNoTax = (energyStorageEquipment * projectChangeFactor) / (1 + VATRate);
    const energyStorageInstallationNoTax = (energyStorageInstallation * projectChangeFactor) / (1 + installationTaxRate);
    const energyStorageEquipmentAndInstallationNoTax = energyStorageEquipmentNoTax + energyStorageInstallationNoTax;
    
    // 储能建筑不含税
    const energyStorageBuildingNoTax = (energyStorageBuilding * projectChangeFactor) / (1 + installationTaxRate);
    
    // 储能其他不含税
    const energyStorageOthersNoTax = energyStorageOthers * projectChangeFactor;
    
    // 储能利息不含税 - 固定为0
    const energyStorageInterestNoTax = 0;
    
    // 固定储能资产不含税
    const fixedEnergyStorageAssetsNoTax = energyStorageEquipmentAndInstallationNoTax + 
                                        energyStorageBuildingNoTax + 
                                        energyStorageOthersNoTax + 
                                        energyStorageInterestNoTax;
    
    // 电解槽设备及安装不含税
    const electrolyzerEquipmentNoTax = (electrolyzerEquipment * projectChangeFactor) / (1 + VATRate);
    const electrolyzerInstallationNoTax = (electrolyzerInstallation * projectChangeFactor) / (1 + installationTaxRate);
    const electrolyzerEquipmentAndInstallationNoTax = electrolyzerEquipmentNoTax + electrolyzerInstallationNoTax;
    
    // 电解槽建筑不含税
    const electrolyzerBuildingNoTax = (electrolyzerBuilding * projectChangeFactor) / (1 + installationTaxRate);
    
    // 电解槽其他不含税
    const electrolyzerOthersNoTax = electrolyzerOthers * projectChangeFactor;
    
    // 电解槽利息不含税 - 使用制氢侧利息占比
    // 这里计算利息按照设备占比分配
    const electrolyzerInterestNoTax = constructionPeriodInterest * (electrolyzerEquipment / totalHydrogenEquipment);
    
    // 固定电解槽资产不含税
    const fixedElectrolyzerAssetsNoTax = electrolyzerEquipmentAndInstallationNoTax + 
                                       electrolyzerBuildingNoTax + 
                                       electrolyzerOthersNoTax + 
                                       electrolyzerInterestNoTax;
    
    // 储罐设备及安装不含税
    const hydrogenStorageEquipmentNoTax = (hydrogenStorageEquipment * projectChangeFactor) / (1 + VATRate);
    const hydrogenStorageInstallationNoTax = (hydrogenStorageInstallation * projectChangeFactor) / (1 + installationTaxRate);
    const hydrogenStorageEquipmentAndInstallationNoTax = hydrogenStorageEquipmentNoTax + hydrogenStorageInstallationNoTax;
    
    // 储罐建筑不含税
    const hydrogenStorageBuildingNoTax = (hydrogenStorageBuilding * projectChangeFactor) / (1 + installationTaxRate);
    
    // 储罐其他不含税
    const hydrogenStorageOthersNoTax = hydrogenStorageOthers * projectChangeFactor;
    
    // 储罐利息不含税 - 使用制氢侧利息占比
    // 这里计算利息按照设备占比分配
    const hydrogenStorageInterestNoTax = constructionPeriodInterest * (hydrogenStorageEquipment / totalHydrogenEquipment);
    
    // 固定储罐资产不含税
    const fixedHydrogenStorageAssetsNoTax = hydrogenStorageEquipmentAndInstallationNoTax + 
                                            hydrogenStorageBuildingNoTax + 
                                            hydrogenStorageOthersNoTax + 
                                            hydrogenStorageInterestNoTax;

    // 总值计算（保持变量名不变，保持兼容性）
    // 计算固定发电资产不含税 = 风机 + 光伏 + 储能
    const fixedPowerAssetsNoTax = fixedWindTurbineAssetsNoTax + fixedPhotovoltaicAssetsNoTax + fixedEnergyStorageAssetsNoTax;
    
    // 计算固定制氢资产不含税 = 电解槽 + 储罐
    const fixedHydrogenAssetsNoTax = fixedElectrolyzerAssetsNoTax + fixedHydrogenStorageAssetsNoTax;
    
    // 固定资产不含税 = 固定发电资产不含税 + 固定制氢资产不含税
    const fixedAssetsNoTax = fixedPowerAssetsNoTax + fixedHydrogenAssetsNoTax;

    // === 计算各组件含税值 ===
    
    // 风机设备及安装含税
    const windTurbineEquipmentWithTax = windTurbineEquipment * projectChangeFactor;
    const windTurbineInstallationWithTax = windTurbineInstallation * projectChangeFactor;
    const windTurbineEquipmentAndInstallationWithTax = (windTurbineEquipmentWithTax + windTurbineInstallationWithTax) * fixedAssetsFinancialFactor;
    
    // 风机建筑含税
    const windTurbineBuildingWithTax = windTurbineBuilding * projectChangeFactor * fixedAssetsFinancialFactor;
    
    // 风机其他含税
    const windTurbineOthersWithTax = windTurbineOthers * projectChangeFactor * fixedAssetsFinancialFactor;
    
    // 风机利息含税 - 固定为0
    const windTurbineInterestWithTax = 0;
    
    // 固定风机资产含税
    const fixedWindTurbineAssetsWithTax = windTurbineEquipmentAndInstallationWithTax + 
                                        windTurbineBuildingWithTax + 
                                        windTurbineOthersWithTax + 
                                        windTurbineInterestWithTax;
    
    // 光伏设备及安装含税
    const photovoltaicEquipmentWithTax = photovoltaicEquipment * projectChangeFactor;
    const photovoltaicInstallationWithTax = photovoltaicInstallation * projectChangeFactor;
    const photovoltaicEquipmentAndInstallationWithTax = (photovoltaicEquipmentWithTax + photovoltaicInstallationWithTax) * fixedAssetsFinancialFactor;
    
    // 光伏建筑含税
    const photovoltaicBuildingWithTax = photovoltaicBuilding * projectChangeFactor * fixedAssetsFinancialFactor;
    
    // 光伏其他含税
    const photovoltaicOthersWithTax = photovoltaicOthers * projectChangeFactor * fixedAssetsFinancialFactor;
    
    // 光伏利息含税 - 固定为0
    const photovoltaicInterestWithTax = 0;
    
    // 固定光伏资产含税
    const fixedPhotovoltaicAssetsWithTax = photovoltaicEquipmentAndInstallationWithTax + 
                                          photovoltaicBuildingWithTax + 
                                          photovoltaicOthersWithTax + 
                                          photovoltaicInterestWithTax;
    
    // 储能设备及安装含税
    const energyStorageEquipmentWithTax = energyStorageEquipment * projectChangeFactor;
    const energyStorageInstallationWithTax = energyStorageInstallation * projectChangeFactor;
    const energyStorageEquipmentAndInstallationWithTax = (energyStorageEquipmentWithTax + energyStorageInstallationWithTax) * fixedAssetsFinancialFactor;
    
    // 储能建筑含税
    const energyStorageBuildingWithTax = energyStorageBuilding * projectChangeFactor * fixedAssetsFinancialFactor;
    
    // 储能其他含税
    const energyStorageOthersWithTax = energyStorageOthers * projectChangeFactor * fixedAssetsFinancialFactor;
    
    // 储能利息含税 - 固定为0
    const energyStorageInterestWithTax = 0;
    
    // 固定储能资产含税
    const fixedEnergyStorageAssetsWithTax = energyStorageEquipmentAndInstallationWithTax + 
                                            energyStorageBuildingWithTax + 
                                            energyStorageOthersWithTax + 
                                            energyStorageInterestWithTax;
    
    // 电解槽设备及安装含税
    const electrolyzerEquipmentWithTax = electrolyzerEquipment * projectChangeFactor;
    const electrolyzerInstallationWithTax = electrolyzerInstallation * projectChangeFactor;
    const electrolyzerEquipmentAndInstallationWithTax = (electrolyzerEquipmentWithTax + electrolyzerInstallationWithTax) * fixedAssetsFinancialFactor;
    
    // 电解槽建筑含税
    const electrolyzerBuildingWithTax = electrolyzerBuilding * projectChangeFactor * fixedAssetsFinancialFactor;
    
    // 电解槽其他含税
    const electrolyzerOthersWithTax = electrolyzerOthers * projectChangeFactor * fixedAssetsFinancialFactor;
    
    // 电解槽利息含税 - 固定为0
    const electrolyzerInterestWithTax = 0;
    
    // 固定电解槽资产含税
    const fixedElectrolyzerAssetsWithTax = electrolyzerEquipmentAndInstallationWithTax + 
                                          electrolyzerBuildingWithTax + 
                                          electrolyzerOthersWithTax + 
                                          electrolyzerInterestWithTax;
    
    // 储罐设备及安装含税
    const hydrogenStorageEquipmentWithTax = hydrogenStorageEquipment * projectChangeFactor;
    const hydrogenStorageInstallationWithTax = hydrogenStorageInstallation * projectChangeFactor;
    const hydrogenStorageEquipmentAndInstallationWithTax = (hydrogenStorageEquipmentWithTax + hydrogenStorageInstallationWithTax) * fixedAssetsFinancialFactor;
    
    // 储罐建筑含税
    const hydrogenStorageBuildingWithTax = hydrogenStorageBuilding * projectChangeFactor * fixedAssetsFinancialFactor;
    
    // 储罐其他含税
    const hydrogenStorageOthersWithTax = hydrogenStorageOthers * projectChangeFactor * fixedAssetsFinancialFactor;
    
    // 储罐利息含税 - 固定为0
    const hydrogenStorageInterestWithTax = 0;
    
    // 固定储罐资产含税
    const fixedHydrogenStorageAssetsWithTax = hydrogenStorageEquipmentAndInstallationWithTax + 
                                               hydrogenStorageBuildingWithTax + 
                                               hydrogenStorageOthersWithTax + 
                                               hydrogenStorageInterestWithTax;

    // 总值计算（保持变量名不变，保持兼容性）
    // 计算固定发电资产含税 = 风机 + 光伏 + 储能
    const fixedPowerAssetsWithTax = fixedWindTurbineAssetsWithTax + fixedPhotovoltaicAssetsWithTax + fixedEnergyStorageAssetsWithTax;
    
    // 计算固定制氢资产含税 = 电解槽 + 储罐
    const fixedHydrogenAssetsWithTax = fixedElectrolyzerAssetsWithTax + fixedHydrogenStorageAssetsWithTax;
    
    // 固定资产含税 = 固定发电资产含税 + 固定制氢资产含税
    const fixedAssetsWithTax = fixedPowerAssetsWithTax + fixedHydrogenAssetsWithTax;

    // 计算发电设备及安装含税（保持变量名不变，保持兼容性）
    const powerEquipmentAndInstallationWithTax = windTurbineEquipmentAndInstallationWithTax + 
                                              photovoltaicEquipmentAndInstallationWithTax + 
                                              energyStorageEquipmentAndInstallationWithTax;
    
    // 计算制氢设备及安装含税（保持变量名不变，保持兼容性）
    const hydrogenEquipmentAndInstallationWithTax = electrolyzerEquipmentAndInstallationWithTax + 
                                                 hydrogenStorageEquipmentAndInstallationWithTax;
    
    // 计算发电设备及安装不含税（保持变量名不变，保持兼容性）
    const powerEquipmentAndInstallationNoTax = windTurbineEquipmentAndInstallationNoTax + 
                                            photovoltaicEquipmentAndInstallationNoTax + 
                                            energyStorageEquipmentAndInstallationNoTax;
    
    // 计算制氢设备及安装不含税（保持变量名不变，保持兼容性）
    const hydrogenEquipmentAndInstallationNoTax = electrolyzerEquipmentAndInstallationNoTax + 
                                               hydrogenStorageEquipmentAndInstallationNoTax;
    
    // 设备及安装不含税 = 发电设备及安装不含税 + 制氢设备及安装不含税
    const equipmentAndInstallationNoTax = powerEquipmentAndInstallationNoTax + hydrogenEquipmentAndInstallationNoTax;

    // 只有在showLogs为true时才打印表格
    if (showLogs) {
        console.log("\n===== 固定资产投资估算表 =====");
        
        // 创建包含结果和中文标题的对象
        const resultsWithChineseTitles = {
            "机电设备及安装工程": mechanicalAndElectricalEquipment,
            "建筑工程": constructionWorks,
            "其他费用": otherExpenses,
            "固定资产静态投资": fixedAssetsStaticInvestment,
            "流动资金": workingCapital,
            "建设期利息": constructionPeriodInterest,
            "项目总投资": projectTotalInvestment,
            "固定发电资产不含税": fixedPowerAssetsNoTax,
            "固定制氢资产不含税": fixedHydrogenAssetsNoTax,
            "固定资产不含税": fixedAssetsNoTax,
            "固定发电资产含税": fixedPowerAssetsWithTax,
            "固定制氢资产含税": fixedHydrogenAssetsWithTax,
            "固定资产含税": fixedAssetsWithTax,
            "设备及安装不含税": equipmentAndInstallationNoTax,
            "发电设备及安装不含税": powerEquipmentAndInstallationNoTax,
            "制氢设备及安装不含税": hydrogenEquipmentAndInstallationNoTax
        };
        
        // 添加拆分组件结果
        const componentsResultsWithChineseTitles = {
            "风机设备及安装不含税": windTurbineEquipmentAndInstallationNoTax,
            "光伏设备及安装不含税": photovoltaicEquipmentAndInstallationNoTax,
            "储能设备及安装不含税": energyStorageEquipmentAndInstallationNoTax,
            "电解槽设备及安装不含税": electrolyzerEquipmentAndInstallationNoTax,
            "储罐设备及安装不含税": hydrogenStorageEquipmentAndInstallationNoTax,
            "固定风机资产不含税": fixedWindTurbineAssetsNoTax,
            "固定光伏资产不含税": fixedPhotovoltaicAssetsNoTax,
            "固定储能资产不含税": fixedEnergyStorageAssetsNoTax,
            "固定电解槽资产不含税": fixedElectrolyzerAssetsNoTax,
            "固定储罐资产不含税": fixedHydrogenStorageAssetsNoTax,
            "风机设备及安装含税": windTurbineEquipmentAndInstallationWithTax,
            "光伏设备及安装含税": photovoltaicEquipmentAndInstallationWithTax,
            "储能设备及安装含税": energyStorageEquipmentAndInstallationWithTax,
            "电解槽设备及安装含税": electrolyzerEquipmentAndInstallationWithTax,
            "储罐设备及安装含税": hydrogenStorageEquipmentAndInstallationWithTax,
            "固定风机资产含税": fixedWindTurbineAssetsWithTax,
            "固定光伏资产含税": fixedPhotovoltaicAssetsWithTax,
            "固定储能资产含税": fixedEnergyStorageAssetsWithTax,
            "固定电解槽资产含税": fixedElectrolyzerAssetsWithTax,
            "固定储罐资产含税": fixedHydrogenStorageAssetsWithTax
        };

        console.table(resultsWithChineseTitles);
        console.log("\n===== 组件拆分结果 =====");
        console.table(componentsResultsWithChineseTitles);
    }

    // 输出结果，保持原有结构
    const results = {
        mechanicalAndElectricalEquipment,
        constructionWorks,
        otherExpenses,
        fixedAssetsStaticInvestment,
        workingCapital,
        constructionPeriodInterest,
        projectTotalInvestment,
        fixedPowerAssetsNoTax,
        fixedHydrogenAssetsNoTax,
        fixedAssetsNoTax,
        fixedPowerAssetsWithTax,
        fixedHydrogenAssetsWithTax,
        fixedAssetsWithTax,
        equipmentAndInstallationNoTax,
        powerEquipmentAndInstallationNoTax,
        powerEquipmentAndInstallationWithTax,
        hydrogenEquipmentAndInstallationNoTax,
        hydrogenEquipmentAndInstallationWithTax,
        equipmentPurchaseCost,
        installationCost,
        
        // 拆分组件结果
        windTurbineEquipmentAndInstallationNoTax,
        photovoltaicEquipmentAndInstallationNoTax,
        energyStorageEquipmentAndInstallationNoTax,
        electrolyzerEquipmentAndInstallationNoTax,
        hydrogenStorageEquipmentAndInstallationNoTax,
        
        fixedWindTurbineAssetsNoTax,
        fixedPhotovoltaicAssetsNoTax,
        fixedEnergyStorageAssetsNoTax,
        fixedElectrolyzerAssetsNoTax,
        fixedHydrogenStorageAssetsNoTax,
        
        windTurbineEquipmentAndInstallationWithTax,
        photovoltaicEquipmentAndInstallationWithTax,
        energyStorageEquipmentAndInstallationWithTax,
        electrolyzerEquipmentAndInstallationWithTax,
        hydrogenStorageEquipmentAndInstallationWithTax,
        
        fixedWindTurbineAssetsWithTax,
        fixedPhotovoltaicAssetsWithTax,
        fixedEnergyStorageAssetsWithTax,
        fixedElectrolyzerAssetsWithTax,
        fixedHydrogenStorageAssetsWithTax
    };

    return results;
}

module.exports = { calculateFixedAssetsInvestmentEstimation }; 