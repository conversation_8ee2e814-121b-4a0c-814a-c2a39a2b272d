# 财务模型 - Node.js版本

## 🚀 构建

```bash
npm run build
```

构建完成后在 `dist/webpack/` 目录生成两个文件：
- `finance-model.min.js` - 基础财务计算模块
- `finance-model-run.min.js` - 包含敏感性分析的完整模块

## 📦 使用方法

### 基础使用
```javascript
const { runFinancialModel } = require('./dist/webpack/finance-model.min.js');

const result = runFinancialModel({
  baseLoanRate: 0.045,
  installedCapacity: 100,
  operatingYears: 25
});
```

### 高级使用（包含敏感性分析）
```javascript
const { getFinanceData } = require('./dist/webpack/finance-model-run.min.js');

const inputs = {
  baseLoanRate: 0.041,
  installedCapacity: 150,
  operatingYears: 25,
  config: {
    epcInfo: {
      pvEPC: 3.5,
      windEPC: 4.2,
      batEPC: 1.5,
      alkEPC: 8.0,
      // ... 更多配置
    }
  }
};

const financeData = getFinanceData(inputs);
console.log('财务结果:', financeData.resultTables);
console.log('敏感性分析:', financeData.resultAnalysis);
```

## ⚡ 特性

- ✅ **Node.js专用优化** - 针对服务器环境优化
- ✅ **高度压缩** - 去除注释和调试代码，最小化文件大小
- ✅ **两个模块** - 基础计算和完整分析分离
- ✅ **生产就绪** - 已移除所有开发调试代码

## 📊 文件大小

- 基础模块: 69KB (压缩后)
- 完整模块: 71KB (压缩后)

简洁高效，专为Node.js生产环境设计。 