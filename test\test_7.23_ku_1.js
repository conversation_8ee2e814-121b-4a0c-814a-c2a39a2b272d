// 财务测算模型测试文件
const { cloneDeep } = require('lodash')
const { getFinanceData } = require('../finance_model/run')
// const { getFinanceData } = require('../dist/run.js')
const fs = require('fs')


const testInputs = {
    "VATRate": 0.13,
    "alkBuildingRatio": 0.314444,
    "alkCapacity": 202,
    "alkEPC": 3.2,
    "alkEquipmentRatio": 0.571003,
    "alkInstallRatio": 0.0803617,
    "alkOthersRatio": 0.03419,
    "alkStoreBuildingRatio": 0.314444,
    "alkStoreCapacity": 0,
    "alkStoreEPC": 2500,
    "alkStoreEquipmentRatio": 0.571003,
    "alkStoreInstallRatio": 0.0803617,
    "alkStoreOthersRatio": 0.03419,
    "baseLoanRate": 0.035,
    "batBuildingRatio": 0.05793,
    "batCapacity": 0,
    "batEPC": 1.25,
    "batEquipmentRatio": 0.8032,
    "batInstallRatio": 0.05573,
    "batOthersRatio": 0.08311,
    "capfirstYearGreenPower": 21378,
    "capfirstYearGridHydrogenProduction": 24.3,
    "capfirstYearHydrogenElectricity": 17602,
    "capfirstYearHydrogenProduction": 333.5,
    "capfirstYearUpGridPower": 3420.48,
    "cityConstructionAndMaintenanceTaxRate": 0.05,
    "config": {
        "initInfo": {
            "totalCost": []
        }
    },
    "customerName": "t",
    "desc": "",
    "discountRate": 0.05,
    "educationSurchargeRate": 0.03,
    "electricityFeeVATRate": 0.13,
    "electrolyzerBuilding": 39558.8160864,
    "electrolyzerDepreciationYears": 20,
    "electrolyzerEquipment": 71835.3750168,
    "electrolyzerInstallation": 10109.95188552,
    "electrolyzerInsuranceRate": 0.0005,
    "electrolyzerMaintCostGrowthRateYears12plus": 0.01,
    "electrolyzerMaintCostGrowthRateYears7to11": 0.01,
    "electrolyzerMaintCostRateYear2": 0.005,
    "electrolyzerMajorOverhaulReplacementRate": 0.3,
    "electrolyzerMaterialsCostAnnualGrowthRate": 0.015,
    "electrolyzerMaterialsCostProvisionRate": 0.003,
    "electrolyzerOtherCostGrowthRate": 0.03,
    "electrolyzerOtherCostUnitPrice": 0.4,
    "electrolyzerOthers": 4301.293464,
    "electrolyzerPowerConsumption": 4.6,
    "energyStorageBuilding": 0,
    "energyStorageDepreciationYears": 10,
    "energyStorageEquipment": 0,
    "energyStorageInstallation": 0,
    "energyStorageInsuranceRate": 0.0005,
    "energyStorageMaintCostGrowthRateYears12plus": 0.02,
    "energyStorageMaintCostGrowthRateYears7to11": 0.015,
    "energyStorageMaintCostRateYear2": 0.004,
    "energyStorageMajorOverhaulReplacementRate": 0.3,
    "energyStorageMaterialsCostAnnualGrowthRate": 0.015,
    "energyStorageMaterialsCostProvisionRate": 0.003,
    "energyStorageOtherCostGrowthRate": 0.03,
    "energyStorageOtherCostUnitPrice": 0.5,
    "energyStorageOthers": 0,
    "equityRatio": 1,
    "financingRatioBase": 0,
    "firstYearDownGridPower": 0.1283,
    "fixedAssetsFinancialFactor": 1,
    "genH2ConsumeWater": 2,
    "gridElectricityPriceNoTax": 0.2504,
    "gridElectricityPriceWithTax": 0.2829,
    "gridHydrogenElectricityPriceNoTax": 0.4398,
    "hydrogenPriceNoTax": 20,
    "hydrogenPriceWithTax": 25,
    "hydrogenProductionCostSensitivityFactor": 1,
    "hydrogenStorageBuilding": 0,
    "hydrogenStorageDepreciationYears": 20,
    "hydrogenStorageEquipment": 0,
    "hydrogenStorageInstallation": 0,
    "hydrogenStorageInsuranceRate": 0.0005,
    "hydrogenStorageMaintCostGrowthRateYears12plus": 0.01,
    "hydrogenStorageMaintCostGrowthRateYears7to11": 0.01,
    "hydrogenStorageMaintCostRateYear2": 0.005,
    "hydrogenStorageMajorOverhaulReplacementRate": 0.3,
    "hydrogenStorageMaterialsCostAnnualGrowthRate": 0.015,
    "hydrogenStorageMaterialsCostProvisionRate": 0.003,
    "hydrogenStorageOtherCostGrowthRate": 0.03,
    "hydrogenStorageOtherCostUnitPrice": 0.1,
    "hydrogenStorageOthers": 0,
    "incomeTaxRate": 0.25,
    "installationTaxRate": 0.09,
    "installedCapacity": 400,
    "isFromCapacity": false,
    "loanTerm": 15,
    "localEducationSurchargeRate": 0.02,
    "maintenanceCostVATRate": 0.13,
    "managerCount": 2,
    "managerMonthlySalary": 0.72,
    "materialsCostVATRate": 0.13,
    "operatingYears": 25,
    "oxygenPriceNoTax": 0,
    "oxygenPriceWithTax": 0,
    "photovoltaicBuilding": 8100.*********,
    "photovoltaicDepreciationYears": 20,
    "photovoltaicEquipment": 112315.30220352,
    "photovoltaicInstallation": 7792.*********,
    "photovoltaicInsuranceRate": 0.0005,
    "photovoltaicLandRentalFeeGrowthRate": 0.01,
    "photovoltaicLandRentalFeeYear2": 202.5,
    "photovoltaicLandTaxAnnual": 5.85828,
    "photovoltaicMaintCostGrowthRateYears12plus": 0.02,
    "photovoltaicMaintCostGrowthRateYears7to11": 0.015,
    "photovoltaicMaintCostRateYear2": 0.004,
    "photovoltaicMajorOverhaulReplacementRate": 0.3,
    "photovoltaicMaterialsCostAnnualGrowthRate": 0.015,
    "photovoltaicMaterialsCostProvisionRate": 0.003,
    "photovoltaicOtherCostGrowthRate": 0.03,
    "photovoltaicOtherCostUnitPrice": 1.5,
    "photovoltaicOthers": 11621.*********,
    "powerGenerationCostSensitivityFactor": 1,
    "powerToHydrogenRatio": 1,
    "projectChangeFactor": 1,
    "projectName": "Ku",
    "projectScene": [
        1,
        0,
        1,
        0,
        1,
        0
    ],
    "projectSceneBinary": [
        1,
        0,
        1,
        0,
        1,
        0
    ],
    "publicLoadCapacity": 0,
    "publicLoadHour": 8000,
    "pvBuildingRatio": 0.05793,
    "pvCapacity": 355.47,
    "pvEPC": 3.5,
    "pvEquipmentRatio": 0.8032,
    "pvFirstYearPowerGenerationHour": 1738.54,
    "pvInstallRatio": 0.05573,
    "pvLandUnitPrice": 0.008425,
    "pvOthersRatio": 0.08311,
    "rateChangeCoefficient": 1,
    "revenueItemVATRate": 0.13,
    "salaryAnnualGrowthRate": 0.05,
    "salvageRate": 0.05,
    "selfPowerPlantElectricityPriceNoTax": 0, //  0.1814
    "selfPowerPlantElectricityPriceWithTax": 0.1814,
    "shareholderDividendRatio": 0,
    "socialSecurityAndWelfareFactor": 0.5,
    "statutorySurplusReserveFundRate": 0.1,
    "technicianCount": 3,
    "technicianMonthlySalary": 0.65,
    "totalCostSensitivityFactor": 1,
    "transportDistance": 0,
    "transportationFeeVATRate": 0.09,
    "transportationRateNoTax": 0,
    "waterFeeVATRate": 0.09,
    "waterPriceNoTax": 2.2,
    "waterResourceTaxUnitPrice": 0.7,
    "windBuildingRatio": 0.05793,
    "windCapacity": 0,
    "windEPC": 3.6,
    "windEquipmentRatio": 0.8032,
    "windFirstYearPowerGenerationHour": 2300,
    "windInstallRatio": 0.05573,
    "windLandUnitPrice": 0.008425,
    "windOthersRatio": 0.08311,
    "windTurbineBuilding": 0,
    "windTurbineDepreciationYears": 20,
    "windTurbineEquipment": 0,
    "windTurbineInstallation": 0,
    "windTurbineInsuranceRate": 0.0005,
    "windTurbineLandRentalFeeGrowthRate": 0.01,
    "windTurbineLandRentalFeeYear2": 135,
    "windTurbineLandTaxAnnual": 3.90552,
    "windTurbineMaintCostGrowthRateYears12plus": 0.02,
    "windTurbineMaintCostGrowthRateYears7to11": 0.015,
    "windTurbineMaintCostRateYear2": 0.004,
    "windTurbineMajorOverhaulReplacementRate": 0.3,
    "windTurbineMaterialsCostAnnualGrowthRate": 0.015,
    "windTurbineMaterialsCostProvisionRate": 0.003,
    "windTurbineOtherCostGrowthRate": 0.03,
    "windTurbineOtherCostUnitPrice": 1,
    "windTurbineOthers": 0,
    "workerCount": 12,
    "workerMonthlySalary": 0.55,
    "yearNDegradationPercentage": 0.0055,
    "yearTwoDegradationPercentage": 0.02,
    electrolyzerPowerConsumption: 4.3,
       firstYearDownGridPower: 34898.490, // 首年下网电量 96699.161-61800.67
   genH2ConsumeWater: 1, // 制氢耗水量(L/Nm³) // 0.86972
//    publicLoadCapacity: 0, // 公辅装置功率(MW) 18.438
// //    publicLoadHour: 8000, // 公辅装置运行小时数
//     // 来自容量系统
//   isFromCapacity: true,

//   capfirstYearGreenPower: 61800.67, // 首年绿电发电量 0
//   capfirstYearHydrogenElectricity: 61800.67, // 首年绿电制氢用电量
//   capfirstYearUpGridPower: 0, // 首年上网电量
//   capfirstYearHydrogenProduction: 129246800, // 首年绿电制氢量 12924.68 * 1000
//   capfirstYearGridHydrogenProduction: 72984965, // 首年电网制氢量 7298.4965 * 1000

//   powerToHydrogenRatio: 1, // 发电制氢比例
//   isFromBe: true
}

const result = getFinanceData(testInputs, true)

// console.log('R:', result)
fs.writeFileSync('./test/result_ku_1.json', JSON.stringify(result, null, 2))


