/**
 * 计算资产负债表
 * @param {Object} params - 输入参数
 * @param {boolean} showLogs - 是否显示表格数据（可选，默认为false）
 * @returns {Object} - 资产负债表结果
 */
function calculateBalanceSheet(params, showLogs = false) {
    const {
        operatingYears, // 项目运营年限
        equityCapital, // 资本金
        endingCashBalance, // 第N年期末现金余额 (已废弃，使用财务计划累计盈余资金代替)
        accumulatedSurplusFunds, // 第N年财务计划累计盈余资金 - 从10财务计划现金流量表获取
        accountsReceivable, // 第N年应收账款
        fixedAssetsNoTax, // 固定资产不含税
        salvageRate, // 残值率
        depreciation, // 第N年折旧费
        amortizationCost, // 第N年摊销费
        payableProfit, // 第N年应付利润
        statutoryReserveFund, // 第N年提取法定盈余公积金
        netProfit, // 第N年净利润
        endingLoanBalance, // 第N年期末借款余额
        previousYearLossCompensation, // 第N年弥补以前年度亏损
        projectTotalInvestment, // 项目总投资
        workingCapital, // 流动资金
        shortTermLoan, // 第N年短期借款
        workingCapitalLoan, // 第N年流动资金借款
        vatDeductibleAssets, // 第N年可抵扣增值税形成的资产
        otherLiabilities, // 第N年负债其他
        inputTaxBeginningBalance // 第N年进项税期初余额
    } = params;

    // 创建结果对象，包括所有年份的数据
    const years = operatingYears + 1; // +1 是因为包括第1年(建设期)
    
    // 确保数组参数有效，针对undefined或null的情况进行处理
    const ensureArray = (arr, length) => {
        if (!arr) return new Array(length).fill(0);
        if (!Array.isArray(arr)) return new Array(length).fill(0);
        return arr.length ? [...arr] : new Array(length).fill(0);
    };
    
    const results = {
        // 资产部分
        totalCurrentAssets: new Array(years).fill(0), // 第N年流动资产总值
        accumulatedSurplusFunds: new Array(years).fill(0), // 第N年累计盈余资金
        currentAssets: new Array(years).fill(0), // 第N年流动资产
        constructionInProgress: new Array(years).fill(0), // 第N年在建工程
        netFixedAssets: new Array(years).fill(0), // 第N年固定资产净值
        netIntangibleAndOtherAssets: new Array(years).fill(0), // 第N年无形及其他资产净值
        vatDeductibleAssets: new Array(years).fill(0), // 第N年可抵扣增值税形成的资产
        assets: new Array(years).fill(0), // 第N年资产
        
        // 负债及所有者权益部分
        totalCurrentLiabilities: new Array(years).fill(0), // 第N年流动负债总额
        currentYearShortTermLoan: new Array(years).fill(0), // 第N年本年短期借款
        otherLiabilities: new Array(years).fill(0), // 第N年负债其他
        constructionInvestmentLoan: new Array(years).fill(0), // 第N年建设投资借款
        workingCapitalLoan: new Array(years).fill(0), // 第N年流动资金借款
        totalLiabilities: new Array(years).fill(0), // 第N年负债小计
        ownersEquity: new Array(years).fill(0), // 第N年所有者权益
        capital: new Array(years).fill(0), // 第N年资本金
        capitalReserve: new Array(years).fill(0), // 第N年资本公积
        accumulatedSurplusReserves: new Array(years).fill(0), // 第N年累计盈余公积金
        accumulatedUndistributedProfits: new Array(years).fill(0), // 第N年累计未分配利润
        totalLiabilitiesAndEquity: new Array(years).fill(0), // 第N年负债及所有者权益
        assetLiabilityRatio: new Array(years).fill(0) // 第N年资产负债率
    };

    // 计算各年数据
    for (let i = 0; i < years; i++) {
        // 资产部分计算
        // 第N年累计盈余资金 = 第N年财务计划累计盈余资金
        const surplusFundsArray = ensureArray(accumulatedSurplusFunds, years);
        results.accumulatedSurplusFunds[i] = surplusFundsArray[i];
        
        // 第N年应收账款
        const receivablesArray = ensureArray(accountsReceivable, years);
        
        // 第N年流动资产 
        if (i === 0) {
            // 第1年流动资产 = 流动资金
            results.currentAssets[i] = workingCapital || 0; // 确保不为undefined
        } else {
            // 第N年流动资产 = 第1年流动资产 + 第N年应收账款
            results.currentAssets[i] = results.currentAssets[0] + receivablesArray[i];
        }
        
        // 第N年流动资产总值 = 第N年累计盈余资金 + 第N年流动资产
        results.totalCurrentAssets[i] = results.accumulatedSurplusFunds[i] + results.currentAssets[i];
        
        // 第N年在建工程 (默认值为0，从经评测算模型)
        results.constructionInProgress[i] = 0;
        
        // 第N年固定资产净值
        if (i === 0) {
            // 第1年固定资产净值 = 项目总投资 - 流动资金 - 第1年可抵扣增值税形成的资产
            const vatAssets = ensureArray(vatDeductibleAssets, years);
            results.netFixedAssets[i] = (projectTotalInvestment || 0) - (workingCapital || 0) - vatAssets[i];
        } else {
            // 第N年固定资产净值 = 第1年固定资产净值 - 累计折旧
            const depreciationArray = ensureArray(depreciation, years);
            let accumulatedDepreciation = 0;
            for (let j = 0; j <= i; j++) {
                accumulatedDepreciation += depreciationArray[j];
            }
            results.netFixedAssets[i] = results.netFixedAssets[0] - accumulatedDepreciation;
        }
        
        // 第N年无形及其他资产净值
        if (i === 0) {
            // 第1年无形及其他资产净值 = 电费等 (经评测算模型说明是第1年电费)
            results.netIntangibleAndOtherAssets[i] = 0; // 电费需要单独传入，这里暂定为0
        } else {
            // 第N(N>=2)年无形及其他资产净值 = 0
            results.netIntangibleAndOtherAssets[i] = 0;
        }
        
        // 第N年可抵扣增值税形成的资产 = 第N年进项税期初余额
        const inputTaxArray = ensureArray(inputTaxBeginningBalance, years);
        results.vatDeductibleAssets[i] = inputTaxArray[i];
        
        // 第N年资产 = 第N年流动资产总值 + 第N年在建工程 + 第N年固定资产净值 + 第N年无形及其他资产净值 + 第N年可抵扣增值税形成的资产
        results.assets[i] = 
            results.totalCurrentAssets[i] + 
            results.constructionInProgress[i] + 
            results.netFixedAssets[i] + 
            results.netIntangibleAndOtherAssets[i] + 
            results.vatDeductibleAssets[i];
        
        // 负债及所有者权益部分计算
        // 第N年本年短期借款 = 第N年短期借款
        const shortLoansArray = ensureArray(shortTermLoan, years);
        results.currentYearShortTermLoan[i] = shortLoansArray[i];
        
        // 第N年负债其他 = 0 (经评测算模型默认值)
        const otherLiabilitiesArray = ensureArray(otherLiabilities, years);
        results.otherLiabilities[i] = otherLiabilitiesArray[i];
        
        // 第N年流动负债总额 = 第N年本年短期借款 + 第N年负债其他
        results.totalCurrentLiabilities[i] = results.currentYearShortTermLoan[i] + results.otherLiabilities[i];
        
        // 第N年建设投资借款 = 第N年期末借款余额
        const loanBalanceArray = ensureArray(endingLoanBalance, years);
        results.constructionInvestmentLoan[i] = loanBalanceArray[i];
        
        // 第N年流动资金借款
        const workingCapLoansArray = ensureArray(workingCapitalLoan, years);
        results.workingCapitalLoan[i] = workingCapLoansArray[i];
        
        // 第N年负债小计 = 第N年流动负债总额 + 第N年建设投资借款 + 第N年流动资金借款
        results.totalLiabilities[i] = 
            results.totalCurrentLiabilities[i] + 
            results.constructionInvestmentLoan[i] + 
            results.workingCapitalLoan[i];
        
        // 第N年资本金 = 资本金
        results.capital[i] = equityCapital || 0;
        
        // 第N年资本公积 (默认值为0)
        results.capitalReserve[i] = 0;
        
        // 第N年累计盈余公积金
        if (i === 0) {
            // 第1年累计盈余公积金 = 第1年提取法定盈余公积金
            const reserveFundArray = ensureArray(statutoryReserveFund, years);
            results.accumulatedSurplusReserves[i] = reserveFundArray[i];
        } else {
            // 第N年累计盈余公积金 = 第N-1年累计盈余公积金 + 第N年提取法定盈余公积金
            const reserveFundArray = ensureArray(statutoryReserveFund, years);
            results.accumulatedSurplusReserves[i] = results.accumulatedSurplusReserves[i-1] + reserveFundArray[i];
        }
        
        // 第N年累计未分配利润 = 第N年未分配利润
        if (i === 0) {
            // 第1年未分配利润 = 第1年净利润 - 第1年提取法定盈余公积金 - 第1年应付股利
            const netProfitArray = ensureArray(netProfit, years);
            const reserveFundArray = ensureArray(statutoryReserveFund, years);
            const payableProfitArray = ensureArray(payableProfit, years);
            results.accumulatedUndistributedProfits[i] = 
                netProfitArray[i] - reserveFundArray[i] - payableProfitArray[i];
        } else {
            // 第N(N>=2)年未分配利润 = 第N-1年未分配利润 + 第N年净利润 - 第N年弥补以前年度亏损 - 第N年提取法定盈余公积金 - 第N年应付股利
            const netProfitArray = ensureArray(netProfit, years);
            const compensationArray = ensureArray(previousYearLossCompensation, years);
            const reserveFundArray = ensureArray(statutoryReserveFund, years);
            const payableProfitArray = ensureArray(payableProfit, years);
            results.accumulatedUndistributedProfits[i] = 
                results.accumulatedUndistributedProfits[i-1] + 
                netProfitArray[i] - compensationArray[i] - 
                reserveFundArray[i] - payableProfitArray[i];
        }
        
        // 第N年所有者权益 = 第N年资本金 + 第N年资本公积 + 第N年累计盈余公积金 + 第N年累计未分配利润
        results.ownersEquity[i] = 
            results.capital[i] + 
            results.capitalReserve[i] + 
            results.accumulatedSurplusReserves[i] + 
            results.accumulatedUndistributedProfits[i];
        
        // 第N年负债及所有者权益 = 第N年负债小计 + 第N年所有者权益
        results.totalLiabilitiesAndEquity[i] = results.totalLiabilities[i] + results.ownersEquity[i];
        
        // 第N年资产负债率 = 第N年负债小计 / 第N年资产
        results.assetLiabilityRatio[i] = results.assets[i] !== 0 ? results.totalLiabilities[i] / results.assets[i] : 0;
    }

    // 按分类打印资产负债表的第一级子项数据
    if (showLogs) {
        console.log('======================= 资产负债表 =======================');
        
        // 创建按年份展示的数据结构
        const balanceSheetByYear = [];

        // 为每一年创建数据对象
        for (let i = 0; i < years; i++) {
            balanceSheetByYear.push({
                '年份': `第${i+1}年`, 
                // 资产部分
                '流动资产总值': results.totalCurrentAssets[i],
                '累计盈余资金': results.accumulatedSurplusFunds[i],
                '流动资产': results.currentAssets[i],
                '在建工程': results.constructionInProgress[i],
                '固定资产净值': results.netFixedAssets[i],
                '无形及其他资产净值': results.netIntangibleAndOtherAssets[i],
                '可抵扣增值税形成的资产': results.vatDeductibleAssets[i],
                '资产': results.assets[i],
                // 负债及所有者权益部分
                '流动负债总额': results.totalCurrentLiabilities[i],
                '本年短期借款': results.currentYearShortTermLoan[i],
                '其他': results.otherLiabilities[i],
                '建设投资借款': results.constructionInvestmentLoan[i],
                '流动资金借款': results.workingCapitalLoan[i],
                '负债小计': results.totalLiabilities[i],
                '所有者权益': results.ownersEquity[i],
                '资本金': results.capital[i],
                '资本公积': results.capitalReserve[i],
                '累计盈余公积金': results.accumulatedSurplusReserves[i],
                '累计未分配利润': results.accumulatedUndistributedProfits[i],
                '负债及所有者权益': results.totalLiabilitiesAndEquity[i],
                '资产负债率': results.assetLiabilityRatio[i]
            });
        }

        // 打印资产部分
        console.log('-------------------- 资产部分 --------------------');
        console.table(balanceSheetByYear.map(yearData => ({
            '年份': yearData['年份'],
            '流动资产总值': yearData['流动资产总值'],
            '累计盈余资金': yearData['累计盈余资金'],
            '流动资产': yearData['流动资产'],
            '在建工程': yearData['在建工程'],
            '固定资产净值': yearData['固定资产净值'],
            '无形及其他资产净值': yearData['无形及其他资产净值'],
            '可抵扣增值税形成的资产': yearData['可抵扣增值税形成的资产'],
            '资产': yearData['资产']
        })));
        
        // 打印负债及所有者权益部分
        console.log('-------------- 负债及所有者权益部分 --------------');
        console.table(balanceSheetByYear.map(yearData => ({
            '年份': yearData['年份'],
            '流动负债总额': yearData['流动负债总额'],
            '本年短期借款': yearData['本年短期借款'],
            '其他': yearData['其他'],
            '建设投资借款': yearData['建设投资借款'],
            '流动资金借款': yearData['流动资金借款'],
            '负债小计': yearData['负债小计'],
            '所有者权益': yearData['所有者权益'],
            '资本金': yearData['资本金'],
            '资本公积': yearData['资本公积'],
            '累计盈余公积金': yearData['累计盈余公积金'],
            '累计未分配利润': yearData['累计未分配利润'],
            '负债及所有者权益': yearData['负债及所有者权益'],
            '资产负债率': yearData['资产负债率']
        })));
    }

    return results;
}

module.exports = { calculateBalanceSheet }; 