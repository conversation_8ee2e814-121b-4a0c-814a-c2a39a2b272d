/**
 * 计算投资计划与资金筹措表
 * @param {Object} params - 输入参数
 * @param {boolean} showLogs - 是否显示表格数据（可选，默认为false）
 * @returns {Object} - 投资计划与资金筹措表结果
 */
function calculateInvestmentPlanAndFundRaising(params, showLogs = false) {
    const {
        projectTotalInvestment, // 项目总投资
        fixedAssetsStaticInvestment, // 固定资产静态投资
        constructionPeriodInterest, // 建设期利息 - 从固定资产投资估算模块获取
        workingCapital, // 流动资金
        bankLoan, // 银行借款
        equityCapital, // 资本金
        financingRatioBase, // 融资比例基准
        loanRate // 借款利率
    } = params;

    // 外资
    const foreignInvestment = 0;
    
    // 内资 = 固定资产静态投资
    const domesticInvestment = fixedAssetsStaticInvestment;
    
    // 建设期外资利息
    const foreignInterestDuringConstruction = 0;
    
    // 建设期内资利息 = 建设期利息（因为建设期外资利息为0）
    // 直接使用从固定资产投资估算模块传入的已计算值
    const domesticInterestDuringConstruction = constructionPeriodInterest;
    
    // 资金筹措 = 项目总投资
    const fundRaising = projectTotalInvestment;
    
    // 长期借款 = 银行借款
    const longTermLoan = bankLoan;
    
    // 建设期利息借款 = 建设期利息
    const constructionPeriodInterestLoan = constructionPeriodInterest;
    
    // 内资借款 = 长期借款 - 建设期利息借款
    const domesticLoan = longTermLoan - constructionPeriodInterestLoan;
    
    // 外资借款
    const foreignLoan = 0;
    
    // 流动资金借款
    const workingCapitalLoan = 0;

    

    // 只有在showLogs为true时才打印表格
    if (showLogs) {
        // 创建包含结果和中文标题的对象
        const resultsWithChineseTitles = {
            "项目总投资": projectTotalInvestment,
            "固定资产静态投资": fixedAssetsStaticInvestment,
            "外资": foreignInvestment,
            "内资": domesticInvestment,
            "建设期利息": constructionPeriodInterest,
            "建设期外资利息": foreignInterestDuringConstruction,
            "建设期内资利息": domesticInterestDuringConstruction,
            "流动资金": workingCapital,
            "资金筹措": fundRaising,
            "资本金": equityCapital,
            "银行借款": bankLoan,
            "长期借款": longTermLoan,
            "内资借款": domesticLoan,
            "外资借款": foreignLoan,
            "建设期利息借款": constructionPeriodInterestLoan,
            "流动资金借款": workingCapitalLoan
        };
        console.log("\n===== 投资计划与资金筹措表 =====");
        console.table(resultsWithChineseTitles);
    }

    // 输出结果
    const results = {
        projectTotalInvestment,
        fixedAssetsStaticInvestment,
        foreignInvestment,
        domesticInvestment,
        constructionPeriodInterest,
        foreignInterestDuringConstruction,
        domesticInterestDuringConstruction,
        workingCapital,
        fundRaising,
        equityCapital,
        bankLoan,
        longTermLoan,
        domesticLoan,
        foreignLoan,
        constructionPeriodInterestLoan,
        workingCapitalLoan
    };

    return results;
}

module.exports = { calculateInvestmentPlanAndFundRaising }; 