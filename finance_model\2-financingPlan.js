/**
 * 计算融资计划
 * @param {Object} params - 输入参数
 * @param {boolean} showLogs - 是否显示表格数据（可选，默认为false）
 * @returns {Object} - 融资计划结果
 */
function calculateFinancingPlan(params, showLogs = false) {
    const {
        projectTotalInvestment, // 项目总投资
        financingRatioBase // 融资比例基准
    } = params;

    // 计算银行借款 = 项目总投资 * 融资比例基准
    const bankLoan = projectTotalInvestment * financingRatioBase;
    
    // 计算资本金 = 项目总投资 - 银行借款
    const equityCapital = projectTotalInvestment - bankLoan;

    

    // 只有在showLogs为true时才打印表格
    if (showLogs) {
        // 创建包含结果和中文标题的对象
        const resultsWithChineseTitles = {
            "项目总投资": projectTotalInvestment,
            "银行借款": bankLoan,
            "资本金": equityCapital
        };
        console.log("\n===== 融资计划表 =====");
        console.table(resultsWithChineseTitles);
    }

    // 输出结果
    const results = {
        projectTotalInvestment,
        bankLoan,
        equityCapital
    };

    return results;
}

module.exports = { calculateFinancingPlan }; 