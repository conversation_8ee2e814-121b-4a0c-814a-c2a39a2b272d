const axios = require('axios');
const { exampleInputs } = require('./example-input');

const BASE_URL = 'http://localhost:9010';

async function testAPI() {

  try {


    const calculateResponse = await axios.post(`${BASE_URL}/api/finance/calculate`, {
      inputs: exampleInputs,
      showLogs: false
    });
    console.log(JSON.stringify(calculateResponse.data, null, 2));
    

  } catch (error) {
    console.error('❌ API测试失败:', error.message);
    if (error.response) {
      console.error('错误响应状态:', error.response.status);
      console.error('错误响应数据:', error.response.data);
    }
  }
}



// 主函数
async function main() {
  console.log('检查服务器是否启动...');
  
  const isServerRunning = await checkServer();
  
  
  console.log('✅ 服务器已启动，开始测试...\n');
  await testAPI();
}

// 如果直接运行这个文件，则执行测试
if (require.main === module) {
  main();
}

module.exports = { testAPI, checkServer }; 