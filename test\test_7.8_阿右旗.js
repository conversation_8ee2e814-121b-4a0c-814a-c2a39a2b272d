// 财务测算模型测试文件
const { cloneDeep } = require('lodash')
const { getFinanceData } = require('../finance_model/run')
// const { getFinanceData } = require('../dist/run.js')
const fs = require('fs')

const testInputs = {
    "VATRate": 0.13,
    "alkBuildingRatio": 0.314444,
    "alkCapacity": 905,
    "alkEPC": 3.2,
    "alkEquipmentRatio": 0.571003,
    "alkInstallRatio": 0.0803617,
    "alkOthersRatio": 0.03419,
    "alkStoreBuildingRatio": 0.314444,
    "alkStoreCapacity": 0,
    "alkStoreEPC": 2500,
    "alkStoreEquipmentRatio": 0.571003,
    "alkStoreInstallRatio": 0.0803617,
    "alkStoreOthersRatio": 0.03419,
    "baseLoanRate": 0.031,
    "batBuildingRatio": 0.05793,
    "batCapacity": 1088.969,
    "batEPC": 1.25,
    "batEquipmentRatio": 0.8032,
    "batInstallRatio": 0.05573,
    "batOthersRatio": 0.08311,
    "capfirstYearGreenPower": 536791.9,
    "capfirstYearGridHydrogenProduction": 478.9,
    "capfirstYearHydrogenElectricity": 487321.55,
    "capfirstYearHydrogenProduction": 8630.1,
    "capfirstYearUpGridPower": 48490.5,
    "cityConstructionAndMaintenanceTaxRate": 0.05,
    "config": {
        "initInfo": {
            "totalCost": []
        }
    },
    "customerName": "内蒙古博源化工",
    "desc": "并网8000h运行",
    "discountRate": 0.05,
    "educationSurchargeRate": 0.03,
    "electricityFeeVATRate": 0.13,
    "electrolyzerBuilding": 91062.9824,
    "electrolyzerDepreciationYears": 20,
    "electrolyzerEquipment": 165362.4688,
    "electrolyzerInstallation": 23272.74832,
    "electrolyzerInsuranceRate": 0.0005,
    "electrolyzerMaintCostGrowthRateYears12plus": 0.01,
    "electrolyzerMaintCostGrowthRateYears7to11": 0.01,
    "electrolyzerMaintCostRateYear2": 0.005,
    "electrolyzerMajorOverhaulReplacementRate": 0.5,
    "electrolyzerMaterialsCostAnnualGrowthRate": 0.015,
    "electrolyzerMaterialsCostProvisionRate": 0.003,
    "electrolyzerOtherCostGrowthRate": 0.03,
    "electrolyzerOtherCostUnitPrice": 0.4,
    "electrolyzerOthers": 9901.424,
    "electrolyzerPowerConsumption": 4.3,
    "energyStorageBuilding": 7885.49677125,
    "energyStorageDepreciationYears": 10,
    "energyStorageEquipment": 109332.4876,
    "energyStorageInstallation": 7586.03029625,
    "energyStorageInsuranceRate": 0.0005,
    "energyStorageMaintCostGrowthRateYears12plus": 0.02,
    "energyStorageMaintCostGrowthRateYears7to11": 0.015,
    "energyStorageMaintCostRateYear2": 0.004,
    "energyStorageMajorOverhaulReplacementRate": 0.6,
    "energyStorageMaterialsCostAnnualGrowthRate": 0.015,
    "energyStorageMaterialsCostProvisionRate": 0.003,
    "energyStorageOtherCostGrowthRate": 0.03,
    "energyStorageOtherCostUnitPrice": 0.5,
    "energyStorageOthers": 11313.02669875,
    "equityRatio": 0.3,
    "financingRatioBase": 0.7,
    "firstYearDownGridPower": 27041,
    "fixedAssetsFinancialFactor": 1,
    "genH2ConsumeWater": 2,
    "gridElectricityPriceNoTax": 0.2504,
    "gridElectricityPriceWithTax": 0.2829,
    "gridHydrogenElectricityPriceNoTax": 0.4398,
    "hydrogenPriceNoTax": 20,
    "hydrogenPriceWithTax": 25,
    "hydrogenProductionCostSensitivityFactor": 1,
    "hydrogenStorageBuilding": 0,
    "hydrogenStorageDepreciationYears": 20,
    "hydrogenStorageEquipment": 0,
    "hydrogenStorageInstallation": 0,
    "hydrogenStorageInsuranceRate": 0.0005,
    "hydrogenStorageMaintCostGrowthRateYears12plus": 0.01,
    "hydrogenStorageMaintCostGrowthRateYears7to11": 0.01,
    "hydrogenStorageMaintCostRateYear2": 0.005,
    "hydrogenStorageMajorOverhaulReplacementRate": 0.3,
    "hydrogenStorageMaterialsCostAnnualGrowthRate": 0.015,
    "hydrogenStorageMaterialsCostProvisionRate": 0.003,
    "hydrogenStorageOtherCostGrowthRate": 0.03,
    "hydrogenStorageOtherCostUnitPrice": 0.1,
    "hydrogenStorageOthers": 0,
    "incomeTaxRate": 0.25,
    "installationTaxRate": 0.09,
    "installedCapacity": 400,
    "isFromCapacity": true,
    "loanTerm": 15,
    "localEducationSurchargeRate": 0.02,
    "maintenanceCostVATRate": 0.13,
    "managerCount": 6,
    "managerMonthlySalary": 0.72,
    "materialsCostVATRate": 0.13,
    "operatingYears": 25,
    "oxygenPriceNoTax": 0,
    "oxygenPriceWithTax": 0,
    "photovoltaicBuilding": 13901.5518915,
    "photovoltaicDepreciationYears": 20,
    "photovoltaicEquipment": 192745.14896,
    "photovoltaicInstallation": 13373.6144815,
    "photovoltaicInsuranceRate": 0.0005,
    "photovoltaicLandRentalFeeGrowthRate": 0.01,
    "photovoltaicLandRentalFeeYear2": 2020.5,
    "photovoltaicLandTaxAnnual": 5.85828,
    "photovoltaicMaintCostGrowthRateYears12plus": 0.02,
    "photovoltaicMaintCostGrowthRateYears7to11": 0.015,
    "photovoltaicMaintCostRateYear2": 0.004,
    "photovoltaicMajorOverhaulReplacementRate": 0.3,
    "photovoltaicMaterialsCostAnnualGrowthRate": 0.015,
    "photovoltaicMaterialsCostProvisionRate": 0.003,
    "photovoltaicOtherCostGrowthRate": 0.03,
    "photovoltaicOtherCostUnitPrice": 1.5,
    "photovoltaicOthers": 19944.0355205,
    "powerGenerationCostSensitivityFactor": 1,
    "powerToHydrogenRatio": 0.8,
    "projectChangeFactor": 1,
    "projectName": "阿右旗风光并网制甲醇项目",
    "projectScene": [
        1,
        1,
        1,
        1,
        1,
        0
    ],
    "publicLoadCapacity": 0,
    "publicLoadHour": 8000,
    "pvBuildingRatio": 0.05793,
    "pvCapacity": 685.633,
    "pvEPC": 3.5,
    "pvEquipmentRatio": 0.8032,
    "pvFirstYearPowerGenerationHour": 1350,
    "pvInstallRatio": 0.05573,
    "pvLandUnitPrice": 0.008425,
    "pvOthersRatio": 0.08311,
    "rateChangeCoefficient": 1,
    "revenueItemVATRate": 0.13,
    "salaryAnnualGrowthRate": 0.05,
    "salvageRate": 0.05,
    "selfPowerPlantElectricityPriceNoTax": 0,
    "selfPowerPlantElectricityPriceWithTax": 0.1814,
    "shareholderDividendRatio": 0,
    "socialSecurityAndWelfareFactor": 0.5,
    "statutorySurplusReserveFundRate": 0.1,
    "technicianCount": 6,
    "technicianMonthlySalary": 0.65,
    "totalCostSensitivityFactor": 1,
    "transportDistance": 0,
    "transportationFeeVATRate": 0.09,
    "transportationRateNoTax": 0,
    "waterFeeVATRate": 0.09,
    "waterPriceNoTax": 4.38,
    "waterResourceTaxUnitPrice": 0.7,
    "windBuildingRatio": 0.05793,
    "windCapacity": 1129.315,
    "windEPC": 3.6,
    "windEquipmentRatio": 0.8032,
    "windFirstYearPowerGenerationHour": 2300,
    "windInstallRatio": 0.05573,
    "windLandUnitPrice": 0.008425,
    "windOthersRatio": 0.08311,
    "windTurbineBuilding": 23551.638462,
    "windTurbineDepreciationYears": 20,
    "windTurbineEquipment": 326543.69088,
    "windTurbineInstallation": 22657.220982,
    "windTurbineInsuranceRate": 0.0005,
    "windTurbineLandRentalFeeGrowthRate": 0.01,
    "windTurbineLandRentalFeeYear2": 135,
    "windTurbineLandTaxAnnual": 3.90552,
    "windTurbineMaintCostGrowthRateYears12plus": 0.02,
    "windTurbineMaintCostGrowthRateYears7to11": 0.015,
    "windTurbineMaintCostRateYear2": 0.004,
    "windTurbineMajorOverhaulReplacementRate": 0.3,
    "windTurbineMaterialsCostAnnualGrowthRate": 0.015,
    "windTurbineMaterialsCostProvisionRate": 0.003,
    "windTurbineOtherCostGrowthRate": 0.03,
    "windTurbineOtherCostUnitPrice": 1,
    "windTurbineOthers": 33788.653074,
    "workerCount": 20,
    "workerMonthlySalary": 0.55,
    "yearNDegradationPercentage": 0.0055,
    "yearTwoDegradationPercentage": 0.02
}

const result = getFinanceData(testInputs, true)

// console.log('R:', result)
fs.writeFileSync('./test/result_7.8_阿右旗.json', JSON.stringify(result, null, 2))


