// 财务测算模型测试文件
const { cloneDeep }  = require('lodash')
const { getFinanceData } = require('../finance_model/run')
// const { getFinanceData } = require('../dist/run.js')
const fs = require('fs')

const testInputs = {
  "VATRate": 0.13,
  "alkBuildingRatio": 0.314444,
  "alkCapacity": 605,
  "alkEPC": 4,
  "alkEquipmentRatio": 0.571003,
  "alkInstallRatio": 0.0803617,
  "alkOthersRatio": 0.03419,
  "alkStoreBuildingRatio": 0.314444,
  "alkStoreCapacity": 0,
  "alkStoreEPC": 2500,
  "alkStoreEquipmentRatio": 0.571003,
  "alkStoreInstallRatio": 0.0803617,
  "alkStoreOthersRatio": 0.03419,
  "baseLoanRate": 0.031,
  "batBuildingRatio": 0.05793,
  "batCapacity": 335,
  "batEPC": 0.9,
  "batEquipmentRatio": 0.8032,
  "batInstallRatio": 0.05573,
  "batOthersRatio": 0.08311,
  "capfirstYearGreenPower": 357971.7,
  "capfirstYearGridHydrogenProduction": 41,
  "capfirstYearHydrogenElectricity": 298624.57999999996,
  "capfirstYearHydrogenProduction": 5283.9,
  "capfirstYearUpGridPower": 0,
  "cityConstructionAndMaintenanceTaxRate": 0.05,
  "config": {
      "initInfo": {
          "totalCost": []
      }
  },
  "customerName": "联合能源",
  "desc": null,
  "discountRate": 0.05,
  "educationSurchargeRate": 0.03,
  "electricityFeeVATRate": 0.13,
  "electrolyzerBuilding": 76095.448,
  "electrolyzerDepreciationYears": 20,
  "electrolyzerEquipment": 138182.726,
  "electrolyzerInstallation": 19447.5314,
  "electrolyzerInsuranceRate": 0.0005,
  "electrolyzerMaintCostGrowthRateYears12plus": 0.01,
  "electrolyzerMaintCostGrowthRateYears7to11": 0.01,
  "electrolyzerMaintCostRateYear2": 0.005,
  "electrolyzerMajorOverhaulReplacementRate": 0.3,
  "electrolyzerMaterialsCostAnnualGrowthRate": 0.015,
  "electrolyzerMaterialsCostProvisionRate": 0.003,
  "electrolyzerOtherCostGrowthRate": 0.03,
  "electrolyzerOtherCostUnitPrice": 0.4,
  "electrolyzerOthers": 8273.98,
  "electrolyzerPowerConsumption": 5,
  "energyStorageBuilding": 1746.5895,
  "energyStorageDepreciationYears": 10,
  "energyStorageEquipment": 24216.48,
  "energyStorageInstallation": 1680.2595,
  "energyStorageInsuranceRate": 0.0005,
  "energyStorageMaintCostGrowthRateYears12plus": 0.02,
  "energyStorageMaintCostGrowthRateYears7to11": 0.015,
  "energyStorageMaintCostRateYear2": 0.004,
  "energyStorageMajorOverhaulReplacementRate": 0.3,
  "energyStorageMaterialsCostAnnualGrowthRate": 0.015,
  "energyStorageMaterialsCostProvisionRate": 0.003,
  "energyStorageOtherCostGrowthRate": 0.03,
  "energyStorageOtherCostUnitPrice": 0.5,
  "energyStorageOthers": 2505.7665,
  "equityRatio": 0.3,
  "financingRatioBase": 0.7,
  "firstYearDownGridPower": 2319.2,
  "fixedAssetsFinancialFactor": 1,
  "genH2ConsumeWater": 2,
  "gridElectricityPriceNoTax": 0.2504,
  "gridElectricityPriceWithTax": 0.2829,
  "gridHydrogenElectricityPriceNoTax": 0.4398,
  "hydrogenPriceNoTax": 20,
  "hydrogenPriceWithTax": 25,
  "hydrogenProductionCostSensitivityFactor": 1,
  "hydrogenStorageBuilding": 0,
  "hydrogenStorageDepreciationYears": 20,
  "hydrogenStorageEquipment": 0,
  "hydrogenStorageInstallation": 0,
  "hydrogenStorageInsuranceRate": 0.0005,
  "hydrogenStorageMaintCostGrowthRateYears12plus": 0.01,
  "hydrogenStorageMaintCostGrowthRateYears7to11": 0.01,
  "hydrogenStorageMaintCostRateYear2": 0.005,
  "hydrogenStorageMajorOverhaulReplacementRate": 0.3,
  "hydrogenStorageMaterialsCostAnnualGrowthRate": 0.015,
  "hydrogenStorageMaterialsCostProvisionRate": 0.003,
  "hydrogenStorageOtherCostGrowthRate": 0.03,
  "hydrogenStorageOtherCostUnitPrice": 0.1,
  "hydrogenStorageOthers": 0,
  "incomeTaxRate": 0.25,
  "installationTaxRate": 0.09,
  "installedCapacity": 400,
  "isFromCapacity": true,
  "loanTerm": 15,
  "localEducationSurchargeRate": 0.02,
  "maintenanceCostVATRate": 0.13,
  "managerCount": 2,
  "managerMonthlySalary": 0.72,
  "materialsCostVATRate": 0.13,
  "operatingYears": 20,
  "oxygenPriceNoTax": 0,
  "oxygenPriceWithTax": 0,
  "photovoltaicBuilding": 9496.17525,
  "photovoltaicDepreciationYears": 20,
  "photovoltaicEquipment": 131664.56,
  "photovoltaicInstallation": 9135.54025,
  "photovoltaicInsuranceRate": 0.0005,
  "photovoltaicLandRentalFeeGrowthRate": 0.01,
  "photovoltaicLandRentalFeeYear2": 202.5,
  "photovoltaicLandTaxAnnual": 5.85828,
  "photovoltaicMaintCostGrowthRateYears12plus": 0.02,
  "photovoltaicMaintCostGrowthRateYears7to11": 0.015,
  "photovoltaicMaintCostRateYear2": 0.004,
  "photovoltaicMajorOverhaulReplacementRate": 0.3,
  "photovoltaicMaterialsCostAnnualGrowthRate": 0.015,
  "photovoltaicMaterialsCostProvisionRate": 0.003,
  "photovoltaicOtherCostGrowthRate": 0.03,
  "photovoltaicOtherCostUnitPrice": 1.5,
  "photovoltaicOthers": 13623.80675,
  "powerGenerationCostSensitivityFactor": 1,
  "powerToHydrogenRatio": 1,
  "projectChangeFactor": 1,
  "projectName": "联合能源",
  "projectScene": [
      1,
      1,
      1,
      1,
      1,
      0
  ],
  "publicLoadCapacity": 0,
  "publicLoadHour": 8000,
  "pvBuildingRatio": 0.05793,
  "pvCapacity": 415,
  "pvEPC": 3.95,
  "pvEquipmentRatio": 0.8032,
  "pvFirstYearPowerGenerationHour": 1235,
  "pvInstallRatio": 0.05573,
  "pvLandUnitPrice": 0.008425,
  "pvOthersRatio": 0.08311,
  "rateChangeCoefficient": 1,
  "revenueItemVATRate": 0.13,
  "salaryAnnualGrowthRate": 0.05,
  "salvageRate": 0.05,
  "selfPowerPlantElectricityPriceNoTax": 0,
  "selfPowerPlantElectricityPriceWithTax": 0.1814,
  "shareholderDividendRatio": 0,
  "socialSecurityAndWelfareFactor": 0.5,
  "statutorySurplusReserveFundRate": 0.1,
  "technicianCount": 3,
  "technicianMonthlySalary": 0.65,
  "totalCostSensitivityFactor": 1,
  "transportDistance": 0,
  "transportationFeeVATRate": 0.09,
  "transportationRateNoTax": 0,
  "waterFeeVATRate": 0.09,
  "waterPriceNoTax": 4.38,
  "waterResourceTaxUnitPrice": 0.7,
  "windBuildingRatio": 0.05793,
  "windCapacity": 700,
  "windEPC": 4.5,
  "windEquipmentRatio": 0.8032,
  "windFirstYearPowerGenerationHour": 2235,
  "windInstallRatio": 0.05573,
  "windLandUnitPrice": 0.008425,
  "windOthersRatio": 0.08311,
  "windTurbineBuilding": 18247.95,
  "windTurbineDepreciationYears": 20,
  "windTurbineEquipment": 253008,
  "windTurbineInstallation": 17554.95,
  "windTurbineInsuranceRate": 0.0005,
  "windTurbineLandRentalFeeGrowthRate": 0.01,
  "windTurbineLandRentalFeeYear2": 135,
  "windTurbineLandTaxAnnual": 3.90552,
  "windTurbineMaintCostGrowthRateYears12plus": 0.02,
  "windTurbineMaintCostGrowthRateYears7to11": 0.015,
  "windTurbineMaintCostRateYear2": 0.004,
  "windTurbineMajorOverhaulReplacementRate": 0.3,
  "windTurbineMaterialsCostAnnualGrowthRate": 0.015,
  "windTurbineMaterialsCostProvisionRate": 0.003,
  "windTurbineOtherCostGrowthRate": 0.03,
  "windTurbineOtherCostUnitPrice": 1,
  "windTurbineOthers": 26179.65,
  "workerCount": 12,
  "workerMonthlySalary": 0.55,
  "yearNDegradationPercentage": 0.0055,
  "yearTwoDegradationPercentage": 0.02
}

const result = getFinanceData(testInputs, true)

// console.log('R:', result)
fs.writeFileSync('./test/result_6.13.json', JSON.stringify(result, null, 2))


